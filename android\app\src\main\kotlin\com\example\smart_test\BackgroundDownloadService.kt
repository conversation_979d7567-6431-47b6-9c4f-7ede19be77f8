package com.example.smart_test

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.net.HttpURLConnection
import java.net.URL
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec
import java.security.MessageDigest

class BackgroundDownloadService : Service() {
    
    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "video_download_channel"
        private const val CHANNEL_NAME = "تحميل الفيديوهات"
        
        private val activeDownloads = mutableMapOf<String, Job>()
        private val downloadProgress = mutableMapOf<String, Int>()
        
        fun cancelDownload(videoId: String) {
            activeDownloads[videoId]?.cancel()
            activeDownloads.remove(videoId)
            downloadProgress.remove(videoId)
        }
        
        fun getDownloadProgress(videoId: String): Int {
            return downloadProgress[videoId] ?: 0
        }
    }
    
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private lateinit var notificationManager: NotificationManager
    
    override fun onCreate() {
        super.onCreate()
        notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        intent?.let { handleDownloadIntent(it) }
        return START_NOT_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "إشعارات تحميل الفيديوهات"
                setSound(null, null)
            }
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun handleDownloadIntent(intent: Intent) {
        val videoId = intent.getStringExtra("videoId") ?: return
        val videoTitle = intent.getStringExtra("videoTitle") ?: return
        val videoUrl = intent.getStringExtra("videoUrl") ?: return
        val quality = intent.getStringExtra("quality") ?: return
        val filePath = intent.getStringExtra("filePath") ?: return
        val deviceId = intent.getStringExtra("deviceId") ?: return
        
        startDownload(videoId, videoTitle, videoUrl, quality, filePath, deviceId)
    }
    
    private fun startDownload(
        videoId: String,
        videoTitle: String,
        videoUrl: String,
        quality: String,
        filePath: String,
        deviceId: String
    ) {
        // إلغاء التحميل السابق إن وجد
        activeDownloads[videoId]?.cancel()
        
        val job = serviceScope.launch {
            try {
                downloadProgress[videoId] = 0
                startForeground(NOTIFICATION_ID, createNotification(videoTitle, 0))
                
                // تحميل الفيديو
                downloadFile(videoUrl, filePath, videoId, videoTitle)
                
                // تشفير الملف
                encryptFile(filePath, deviceId)
                
                // إشعار الاكتمال
                showCompletionNotification(videoTitle)
                
            } catch (e: CancellationException) {
                // تم إلغاء التحميل
                showCancellationNotification(videoTitle)
            } catch (e: Exception) {
                // فشل التحميل
                showErrorNotification(videoTitle, e.message ?: "خطأ غير معروف")
            } finally {
                activeDownloads.remove(videoId)
                downloadProgress.remove(videoId)
                if (activeDownloads.isEmpty()) {
                    stopForeground(true)
                    stopSelf()
                }
            }
        }
        
        activeDownloads[videoId] = job
    }
    
    private suspend fun downloadFile(url: String, filePath: String, videoId: String, videoTitle: String) {
        withContext(Dispatchers.IO) {
            val connection = URL(url).openConnection() as HttpURLConnection
            connection.connect()
            
            val fileLength = connection.contentLength
            val input = connection.inputStream
            val output = FileOutputStream(File(filePath))
            
            val buffer = ByteArray(8192)
            var total = 0
            var count: Int
            
            while (input.read(buffer).also { count = it } != -1) {
                if (!isActive) throw CancellationException("تم إلغاء التحميل")
                
                total += count
                output.write(buffer, 0, count)
                
                // تحديث التقدم
                if (fileLength > 0) {
                    val progress = (total * 100 / fileLength)
                    downloadProgress[videoId] = progress
                    
                    // تحديث الإشعار كل 5%
                    if (progress % 5 == 0) {
                        updateNotification(videoTitle, progress)
                    }
                }
            }
            
            output.close()
            input.close()
            connection.disconnect()
        }
    }
    
    private fun encryptFile(filePath: String, deviceId: String) {
        val file = File(filePath)
        val data = file.readBytes()
        
        // تشفير بسيط باستخدام AES
        val key = generateKey(deviceId)
        val cipher = Cipher.getInstance("AES")
        cipher.init(Cipher.ENCRYPT_MODE, SecretKeySpec(key, "AES"))
        val encryptedData = cipher.doFinal(data)
        
        file.writeBytes(encryptedData)
    }
    
    private fun generateKey(deviceId: String): ByteArray {
        val digest = MessageDigest.getInstance("SHA-256")
        val hash = digest.digest(deviceId.toByteArray())
        return hash.copyOf(16) // استخدام أول 16 بايت للمفتاح
    }
    
    private fun createNotification(title: String, progress: Int): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("تحميل: $title")
            .setContentText("$progress%")
            .setSmallIcon(android.R.drawable.stat_sys_download)
            .setProgress(100, progress, false)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .build()
    }
    
    private fun updateNotification(title: String, progress: Int) {
        val notification = createNotification(title, progress)
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
    
    private fun showCompletionNotification(title: String) {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("اكتمل التحميل")
            .setContentText(title)
            .setSmallIcon(android.R.drawable.stat_sys_download_done)
            .setAutoCancel(true)
            .build()
        
        notificationManager.notify(NOTIFICATION_ID + 1, notification)
    }
    
    private fun showCancellationNotification(title: String) {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("تم إلغاء التحميل")
            .setContentText(title)
            .setSmallIcon(android.R.drawable.stat_notify_error)
            .setAutoCancel(true)
            .build()
        
        notificationManager.notify(NOTIFICATION_ID + 2, notification)
    }
    
    private fun showErrorNotification(title: String, error: String) {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("فشل التحميل")
            .setContentText("$title: $error")
            .setSmallIcon(android.R.drawable.stat_notify_error)
            .setAutoCancel(true)
            .build()
        
        notificationManager.notify(NOTIFICATION_ID + 3, notification)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        serviceScope.cancel()
    }
}
