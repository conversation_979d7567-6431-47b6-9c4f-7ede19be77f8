package com.example.smart_test

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.content.Intent

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.smarttest.student/background_download"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "startDownload" -> {
                    val videoId = call.argument<String>("videoId")!!
                    val videoTitle = call.argument<String>("videoTitle")!!
                    val videoUrl = call.argument<String>("videoUrl")!!
                    val quality = call.argument<String>("quality")!!
                    val filePath = call.argument<String>("filePath")!!
                    val deviceId = call.argument<String>("deviceId")!!

                    // بدء خدمة التحميل في الخلفية
                    val intent = Intent(this, BackgroundDownloadService::class.java).apply {
                        putExtra("videoId", videoId)
                        putExtra("videoTitle", videoTitle)
                        putExtra("videoUrl", videoUrl)
                        putExtra("quality", quality)
                        putExtra("filePath", filePath)
                        putExtra("deviceId", deviceId)
                    }
                    startForegroundService(intent)
                    result.success(true)
                }
                "cancelDownload" -> {
                    val videoId = call.argument<String>("videoId")!!
                    BackgroundDownloadService.cancelDownload(videoId)
                    result.success(true)
                }
                "getDownloadProgress" -> {
                    val videoId = call.argument<String>("videoId")!!
                    val progress = BackgroundDownloadService.getDownloadProgress(videoId)
                    result.success(progress)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
}
