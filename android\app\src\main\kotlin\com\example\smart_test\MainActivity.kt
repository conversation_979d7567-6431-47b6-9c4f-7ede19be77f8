package com.example.smart_test

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.smarttest.background_download"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        val methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
        VideoDownloadService.setMethodChannel(methodChannel)

        methodChannel.setMethodCallHandler { call, result ->
            when (call.method) {
                "startBackgroundDownload" -> {
                    val videoId = call.argument<String>("videoId")!!
                    val videoTitle = call.argument<String>("videoTitle")!!
                    val videoUrl = call.argument<String>("videoUrl")!!
                    val quality = call.argument<String>("quality")!!
                    val filePath = call.argument<String>("filePath")!!

                    VideoDownloadService.startDownload(
                        this,
                        videoId,
                        videoTitle,
                        videoUrl,
                        quality,
                        filePath
                    )
                    result.success(true)
                }
                "cancelBackgroundDownload" -> {
                    val videoId = call.argument<String>("videoId")!!
                    VideoDownloadService.cancelDownload(this, videoId)
                    result.success(true)
                }
                "cancelAllBackgroundDownloads" -> {
                    VideoDownloadService.cancelAllDownloads(this)
                    result.success(true)
                }
                "isDownloading" -> {
                    val videoId = call.argument<String>("videoId")!!
                    val isDownloading = VideoDownloadService.isDownloading(videoId)
                    result.success(isDownloading)
                }
                "getDownloadProgress" -> {
                    val videoId = call.argument<String>("videoId")!!
                    val progress = VideoDownloadService.getDownloadProgress(videoId)
                    result.success(progress)
                }
                "updateDownloadProgress" -> {
                    val videoId = call.argument<String>("videoId")!!
                    val progress = call.argument<Int>("progress")!!
                    VideoDownloadService.updateDownloadProgress(videoId, progress)
                    result.success(true)
                }
                "notifyDownloadComplete" -> {
                    val videoId = call.argument<String>("videoId")!!
                    VideoDownloadService.notifyDownloadComplete(videoId)
                    result.success(true)
                }
                "notifyDownloadFailed" -> {
                    val videoId = call.argument<String>("videoId")!!
                    VideoDownloadService.notifyDownloadFailed(videoId)
                    result.success(true)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
}
