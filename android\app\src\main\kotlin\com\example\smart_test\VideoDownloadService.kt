package com.example.smart_test

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap

class VideoDownloadService : Service() {
    companion object {
        const val CHANNEL_ID = "video_download_channel"
        const val NOTIFICATION_ID = 1001
        const val ACTION_START_DOWNLOAD = "START_DOWNLOAD"
        const val ACTION_CANCEL_DOWNLOAD = "CANCEL_DOWNLOAD"
        const val ACTION_CANCEL_ALL = "CANCEL_ALL"
        
        const val EXTRA_VIDEO_ID = "video_id"
        const val EXTRA_VIDEO_TITLE = "video_title"
        const val EXTRA_VIDEO_URL = "video_url"
        const val EXTRA_QUALITY = "quality"
        const val EXTRA_FILE_PATH = "file_path"
        
        private var methodChannel: MethodChannel? = null
        private val activeDownloads = ConcurrentHashMap<String, Job>()
        private val downloadProgress = ConcurrentHashMap<String, Int>()
        
        fun setMethodChannel(channel: MethodChannel) {
            methodChannel = channel
        }
        
        fun startDownload(
            context: Context,
            videoId: String,
            videoTitle: String,
            videoUrl: String,
            quality: String,
            filePath: String
        ) {
            val intent = Intent(context, VideoDownloadService::class.java).apply {
                action = ACTION_START_DOWNLOAD
                putExtra(EXTRA_VIDEO_ID, videoId)
                putExtra(EXTRA_VIDEO_TITLE, videoTitle)
                putExtra(EXTRA_VIDEO_URL, videoUrl)
                putExtra(EXTRA_QUALITY, quality)
                putExtra(EXTRA_FILE_PATH, filePath)
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        
        fun cancelDownload(context: Context, videoId: String) {
            val intent = Intent(context, VideoDownloadService::class.java).apply {
                action = ACTION_CANCEL_DOWNLOAD
                putExtra(EXTRA_VIDEO_ID, videoId)
            }
            context.startService(intent)
        }
        
        fun cancelAllDownloads(context: Context) {
            val intent = Intent(context, VideoDownloadService::class.java).apply {
                action = ACTION_CANCEL_ALL
            }
            context.startService(intent)
        }
        
        fun isDownloading(videoId: String): Boolean {
            return activeDownloads.containsKey(videoId)
        }
        
        fun getDownloadProgress(videoId: String): Int {
            return downloadProgress[videoId] ?: 0
        }

        fun updateDownloadProgress(videoId: String, progress: Int) {
            downloadProgress[videoId] = progress
        }

        fun notifyDownloadComplete(videoId: String) {
            activeDownloads.remove(videoId)
            downloadProgress.remove(videoId)
        }

        fun notifyDownloadFailed(videoId: String) {
            activeDownloads.remove(videoId)
            downloadProgress.remove(videoId)
        }
    }
    
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_DOWNLOAD -> {
                val videoId = intent.getStringExtra(EXTRA_VIDEO_ID) ?: return START_NOT_STICKY
                val videoTitle = intent.getStringExtra(EXTRA_VIDEO_TITLE) ?: "فيديو"
                val videoUrl = intent.getStringExtra(EXTRA_VIDEO_URL) ?: return START_NOT_STICKY
                val quality = intent.getStringExtra(EXTRA_QUALITY) ?: "480p"
                val filePath = intent.getStringExtra(EXTRA_FILE_PATH) ?: return START_NOT_STICKY
                
                startDownloadJob(videoId, videoTitle, videoUrl, quality, filePath)
            }
            ACTION_CANCEL_DOWNLOAD -> {
                val videoId = intent.getStringExtra(EXTRA_VIDEO_ID) ?: return START_NOT_STICKY
                cancelDownloadJob(videoId)
            }
            ACTION_CANCEL_ALL -> {
                cancelAllDownloadJobs()
            }
        }
        
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "تحميل الفيديوهات",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "إشعارات تحميل الفيديوهات"
                setSound(null, null)
                enableVibration(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun startDownloadJob(
        videoId: String,
        videoTitle: String,
        videoUrl: String,
        quality: String,
        filePath: String
    ) {
        // إلغاء التحميل السابق إن وجد
        activeDownloads[videoId]?.cancel()
        
        val job = serviceScope.launch {
            try {
                downloadProgress[videoId] = 0
                updateNotification("جاري تحميل: $videoTitle", 0)
                
                // استدعاء دالة التحميل من Flutter
                withContext(Dispatchers.Main) {
                    methodChannel?.invokeMethod("downloadVideoInBackground", mapOf(
                        "videoId" to videoId,
                        "videoTitle" to videoTitle,
                        "videoUrl" to videoUrl,
                        "quality" to quality,
                        "filePath" to filePath
                    ))
                }

                // مراقبة تقدم التحميل
                var lastProgress = 0
                while (activeDownloads.containsKey(videoId)) {
                    delay(1000)
                    val progress = downloadProgress[videoId] ?: 0
                    if (progress != lastProgress) {
                        updateNotification("جاري تحميل: $videoTitle", progress)
                        lastProgress = progress
                    }

                    // التحقق من اكتمال التحميل
                    if (progress >= 100) {
                        break
                    }
                }
                
            } catch (e: CancellationException) {
                downloadProgress.remove(videoId)
                withContext(Dispatchers.Main) {
                    methodChannel?.invokeMethod("downloadCancelled", mapOf("videoId" to videoId))
                }
            } catch (e: Exception) {
                downloadProgress.remove(videoId)
                withContext(Dispatchers.Main) {
                    methodChannel?.invokeMethod("downloadFailed", mapOf(
                        "videoId" to videoId,
                        "error" to e.message
                    ))
                }
            } finally {
                activeDownloads.remove(videoId)
                if (activeDownloads.isEmpty()) {
                    stopForeground(true)
                    stopSelf()
                }
            }
        }
        
        activeDownloads[videoId] = job
        startForeground(NOTIFICATION_ID, createNotification("بدء التحميل...", 0))
    }
    
    private fun cancelDownloadJob(videoId: String) {
        activeDownloads[videoId]?.cancel()
        activeDownloads.remove(videoId)
        downloadProgress.remove(videoId)
        
        if (activeDownloads.isEmpty()) {
            stopForeground(true)
            stopSelf()
        }
    }
    
    private fun cancelAllDownloadJobs() {
        activeDownloads.values.forEach { it.cancel() }
        activeDownloads.clear()
        downloadProgress.clear()
        stopForeground(true)
        stopSelf()
    }
    
    private fun createNotification(title: String, progress: Int): Notification {
        val cancelIntent = Intent(this, VideoDownloadService::class.java).apply {
            action = ACTION_CANCEL_ALL
        }
        val cancelPendingIntent = PendingIntent.getService(
            this, 0, cancelIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText("$progress%")
            .setSmallIcon(android.R.drawable.stat_sys_download)
            .setProgress(100, progress, false)
            .setOngoing(true)
            .addAction(android.R.drawable.ic_menu_close_clear_cancel, "إلغاء", cancelPendingIntent)
            .build()
    }
    
    private fun updateNotification(title: String, progress: Int) {
        val notification = createNotification(title, progress)
        val notificationManager = NotificationManagerCompat.from(this)
        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    override fun onDestroy() {
        super.onDestroy()
        serviceScope.cancel()
    }
}
