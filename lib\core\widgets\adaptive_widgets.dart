import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../utils/adaptive_sizing.dart';
import '../theme/app_theme.dart';

/// نص تكيفي يتغير حجمه حسب نوع الجهاز
class AdaptiveText extends StatelessWidget {
  final String text;
  final double fontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextStyle? style;

  const AdaptiveText(
    this.text, {
    super.key,
    required this.fontSize,
    this.fontWeight,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: (style ?? const TextStyle()).copyWith(
        fontSize: fontSize.adaptiveText,
        fontWeight: fontWeight,
        color: color,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// أيقونة تكيفية تتغير حجمها حسب نوع الجهاز
class AdaptiveIcon extends StatelessWidget {
  final IconData icon;
  final double size;
  final Color? color;

  const AdaptiveIcon(this.icon, {super.key, required this.size, this.color});

  @override
  Widget build(BuildContext context) {
    return Icon(icon, size: size.adaptiveIcon, color: color);
  }
}

/// زر تكيفي يتغير حجمه حسب نوع الجهاز
class AdaptiveElevatedButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final Size? minimumSize;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const AdaptiveElevatedButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.minimumSize,
    this.padding,
    this.borderRadius,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final adaptive = AdaptiveSizing.instance;

    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        minimumSize: minimumSize != null
            ? adaptive.adaptiveButtonSize(minimumSize!)
            : Size(
                120.w * adaptive.buttonScaleFactor,
                48.h * adaptive.buttonScaleFactor,
              ),
        padding:
            padding?.adaptive ??
            EdgeInsets.symmetric(
              horizontal: 16.w * adaptive.spacingScaleFactor,
              vertical: 12.h * adaptive.spacingScaleFactor,
            ),
        shape: RoundedRectangleBorder(
          borderRadius:
              borderRadius ?? BorderRadius.circular(12.adaptiveRadius),
        ),
        backgroundColor: backgroundColor ?? AppTheme.primaryColor,
        foregroundColor: foregroundColor ?? Colors.white,
      ),
      child: child,
    );
  }
}

/// بطاقة تكيفية للأقسام والمواد
class AdaptiveSectionCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Color backgroundColor;
  final VoidCallback? onTap;
  final Widget? leading;
  final Widget? trailing;
  final double? elevation;

  const AdaptiveSectionCard({
    super.key,
    required this.title,
    this.subtitle,
    required this.backgroundColor,
    this.onTap,
    this.leading,
    this.trailing,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    final adaptive = AdaptiveSizing.instance;

    return Card(
      elevation: elevation ?? 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.adaptiveRadius),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16.adaptiveRadius),
        child: Container(
          padding: EdgeInsets.all(16.w * adaptive.spacingScaleFactor),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [backgroundColor, backgroundColor.withValues(alpha: 0.8)],
            ),
            borderRadius: BorderRadius.circular(16.adaptiveRadius),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (leading != null) ...[
                leading!,
                SizedBox(height: 12.adaptiveSpacing),
              ],
              AdaptiveText(
                title,
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              if (subtitle != null) ...[
                SizedBox(height: 8.adaptiveSpacing),
                AdaptiveText(
                  subtitle!,
                  fontSize: 12,
                  color: Colors.white.withValues(alpha: 0.9),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              if (trailing != null) ...[
                SizedBox(height: 12.adaptiveSpacing),
                trailing!,
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// شبكة تكيفية للأقسام
class AdaptiveGridView extends StatelessWidget {
  final List<Widget> children;
  final int? crossAxisCount;
  final double? childAspectRatio;
  final double? crossAxisSpacing;
  final double? mainAxisSpacing;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const AdaptiveGridView({
    super.key,
    required this.children,
    this.crossAxisCount,
    this.childAspectRatio,
    this.crossAxisSpacing,
    this.mainAxisSpacing,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    final adaptive = AdaptiveSizing.instance;

    return GridView.builder(
      padding: padding?.adaptive ?? EdgeInsets.all(16.adaptiveSpacing),
      shrinkWrap: shrinkWrap,
      physics: physics,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount ?? adaptive.adaptiveGridColumns(),
        childAspectRatio:
            childAspectRatio ?? adaptive.adaptiveCardAspectRatio(),
        crossAxisSpacing: crossAxisSpacing ?? adaptive.adaptiveGridSpacing(),
        mainAxisSpacing: mainAxisSpacing ?? adaptive.adaptiveGridSpacing(),
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }
}

/// حاوي تكيفي يحدد العرض الأقصى للمحتوى
class AdaptiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final Color? backgroundColor;
  final bool centerContent;

  const AdaptiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.backgroundColor,
    this.centerContent = true,
  });

  @override
  Widget build(BuildContext context) {
    final adaptive = AdaptiveSizing.instance;

    Widget content = Container(
      width: adaptive.maxContentWidth,
      padding: padding?.adaptive,
      color: backgroundColor,
      child: child,
    );

    if (centerContent && adaptive.deviceType == AdaptiveDeviceType.desktop) {
      content = Center(child: content);
    }

    return content;
  }
}

/// شريط تطبيق تكيفي
class AdaptiveAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final Color? backgroundColor;
  final Gradient? gradient;

  const AdaptiveAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.backgroundColor,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    final adaptive = AdaptiveSizing.instance;

    return AppBar(
      title: AdaptiveText(
        title,
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? Colors.transparent,
      elevation: 0,
      leading: leading,
      actions: actions,
      flexibleSpace: gradient != null
          ? Container(decoration: BoxDecoration(gradient: gradient))
          : null,
      toolbarHeight: (kToolbarHeight * adaptive.spacingScaleFactor).h,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
    (kToolbarHeight * AdaptiveSizing.instance.spacingScaleFactor).h,
  );
}
