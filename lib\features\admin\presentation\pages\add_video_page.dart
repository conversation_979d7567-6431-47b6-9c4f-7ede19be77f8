import 'package:flutter/material.dart';
import '../../../../shared/models/video_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/theme/app_theme.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/loading_widget.dart';

/// صفحة إضافة/تعديل الفيديو
class AddVideoPage extends StatefulWidget {
  final String lessonId;
  final Video? video;

  const AddVideoPage({super.key, required this.lessonId, this.video});

  @override
  State<AddVideoPage> createState() => _AddVideoPageState();
}

class _AddVideoPageState extends State<AddVideoPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _url360Controller = TextEditingController();
  final _url480Controller = TextEditingController();
  final _url720Controller = TextEditingController();
  final _url1080Controller = TextEditingController();
  final _orderController = TextEditingController();
  final _durationController = TextEditingController();

  final VideoService _videoService = VideoService.instance;
  bool _isActive = true;
  bool _isLoading = false;
  String _selectedQuality = '720p';

  final List<String> _qualityOptions = ['360p', '480p', '720p', '1080p'];

  bool get _isEditing => widget.video != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _loadVideoData();
    }
  }

  void _loadVideoData() {
    final video = widget.video!;
    _titleController.text = video.title;
    _descriptionController.text = video.description;
    _url360Controller.text = video.encryptedUrl360 ?? '';
    _url480Controller.text = video.encryptedUrl480 ?? '';
    _url720Controller.text = video.encryptedUrl720 ?? '';
    _url1080Controller.text = video.encryptedUrl1080 ?? '';
    _orderController.text = video.order.toString();
    _durationController.text = video.durationInSeconds.toString();
    _isActive = video.isActive;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _url360Controller.dispose();
    _url480Controller.dispose();
    _url720Controller.dispose();
    _url1080Controller.dispose();
    _orderController.dispose();
    _durationController.dispose();
    super.dispose();
  }

  Future<void> _saveVideo() async {
    if (!_formKey.currentState!.validate()) return;

    // التحقق من وجود رابط واحد على الأقل
    if (_url360Controller.text.trim().isEmpty &&
        _url480Controller.text.trim().isEmpty &&
        _url720Controller.text.trim().isEmpty &&
        _url1080Controller.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب إدخال رابط واحد على الأقل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final video = Video(
        id: _isEditing ? widget.video!.id : '',
        sectionId: _isEditing ? widget.video!.sectionId : '',
        subjectId: _isEditing ? widget.video!.subjectId : '',
        unitId: _isEditing ? widget.video!.unitId : '',
        lessonId: widget.lessonId,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        encryptedUrl360: _url360Controller.text.trim().isNotEmpty
            ? _url360Controller.text.trim()
            : null,
        encryptedUrl480: _url480Controller.text.trim().isNotEmpty
            ? _url480Controller.text.trim()
            : null,
        encryptedUrl720: _url720Controller.text.trim().isNotEmpty
            ? _url720Controller.text.trim()
            : null,
        encryptedUrl1080: _url1080Controller.text.trim().isNotEmpty
            ? _url1080Controller.text.trim()
            : null,
        thumbnailUrl: _isEditing ? widget.video!.thumbnailUrl : '',
        durationInSeconds: int.tryParse(_durationController.text) ?? 0,
        fileSizeInBytes: _isEditing ? widget.video!.fileSizeInBytes : 0,
        order: int.tryParse(_orderController.text) ?? 0,
        downloadStatus: _isEditing
            ? widget.video!.downloadStatus
            : VideoDownloadStatus.notDownloaded,
        localEncryptedPath: _isEditing
            ? widget.video!.localEncryptedPath
            : null,
        isActive: _isActive,
        createdAt: _isEditing ? widget.video!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
        createdByAdminId: _isEditing ? widget.video!.createdByAdminId : 'admin',
      );

      if (_isEditing) {
        await _videoService.updateVideo(video);
      } else {
        await _videoService.addVideo(video);
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditing ? 'تم تحديث الفيديو بنجاح' : 'تم إضافة الفيديو بنجاح',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حفظ الفيديو: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل الفيديو' : 'إضافة فيديو جديد'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildVideoInfo(),
                    const SizedBox(height: 24),
                    _buildFormFields(),
                    const SizedBox(height: 32),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildVideoInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.video_library, color: Colors.blue, size: 24),
                const SizedBox(width: 8),
                Text(
                  _isEditing ? 'تعديل الفيديو' : 'فيديو جديد',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _isEditing
                  ? 'قم بتعديل بيانات الفيديو'
                  : 'أدخل بيانات الفيديو الجديد',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        TextFormField(
          controller: _titleController,
          decoration: const InputDecoration(
            labelText: 'عنوان الفيديو',
            hintText: 'مثال: شرح الدرس الأول، حل التمارين',
            prefixIcon: Icon(Icons.title),
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال عنوان الفيديو';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'وصف الفيديو (اختياري)',
            hintText: 'وصف مختصر عن محتوى الفيديو',
            prefixIcon: Icon(Icons.description),
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        const SizedBox(height: 16),
        _buildQualityUrlFields(),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _orderController,
                decoration: const InputDecoration(
                  labelText: 'ترتيب الفيديو',
                  hintText: '1، 2، 3...',
                  prefixIcon: Icon(Icons.sort),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال ترتيب الفيديو';
                  }
                  final order = int.tryParse(value);
                  if (order == null || order < 0) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _durationController,
                decoration: const InputDecoration(
                  labelText: 'مدة الفيديو (ثانية)',
                  hintText: '300، 600، 1200...',
                  prefixIcon: Icon(Icons.access_time),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال مدة الفيديو';
                  }
                  final duration = int.tryParse(value);
                  if (duration == null || duration <= 0) {
                    return 'يرجى إدخال مدة صحيحة';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildQualityDropdown(),
        const SizedBox(height: 20),
        _buildActiveSwitch(),
      ],
    );
  }

  Widget _buildQualityDropdown() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'جودة الفيديو',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _selectedQuality,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.high_quality),
              ),
              items: _qualityOptions.map((quality) {
                return DropdownMenuItem(value: quality, child: Text(quality));
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() => _selectedQuality = value);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveSwitch() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              _isActive ? Icons.visibility : Icons.visibility_off,
              color: _isActive ? Colors.green : Colors.grey,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'حالة الفيديو',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    _isActive
                        ? 'الفيديو نشط ومرئي للطلاب'
                        : 'الفيديو غير نشط ومخفي',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Switch(
              value: _isActive,
              onChanged: (value) {
                setState(() => _isActive = value);
              },
              activeColor: Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[300],
              foregroundColor: Colors.black87,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('إلغاء'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _saveVideo,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: Text(_isEditing ? 'تحديث الفيديو' : 'إضافة الفيديو'),
          ),
        ),
      ],
    );
  }

  Widget _buildQualityUrlFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'روابط الفيديو بجودات مختلفة (Google Drive)',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        const Text(
          'يمكنك إضافة رابط واحد أو أكثر حسب الجودات المتوفرة',
          style: TextStyle(fontSize: 14, color: Colors.grey),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _url360Controller,
          decoration: const InputDecoration(
            labelText: 'رابط جودة 360p (اختياري)',
            hintText: 'https://drive.google.com/file/d/...',
            prefixIcon: Icon(Icons.video_file),
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value != null && value.trim().isNotEmpty) {
              if (!value.contains('drive.google.com')) {
                return 'يرجى إدخال رابط Google Drive صحيح';
              }
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _url480Controller,
          decoration: const InputDecoration(
            labelText: 'رابط جودة 480p (اختياري)',
            hintText: 'https://drive.google.com/file/d/...',
            prefixIcon: Icon(Icons.video_file),
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value != null && value.trim().isNotEmpty) {
              if (!value.contains('drive.google.com')) {
                return 'يرجى إدخال رابط Google Drive صحيح';
              }
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _url720Controller,
          decoration: const InputDecoration(
            labelText: 'رابط جودة 720p (اختياري)',
            hintText: 'https://drive.google.com/file/d/...',
            prefixIcon: Icon(Icons.video_file),
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value != null && value.trim().isNotEmpty) {
              if (!value.contains('drive.google.com')) {
                return 'يرجى إدخال رابط Google Drive صحيح';
              }
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _url1080Controller,
          decoration: const InputDecoration(
            labelText: 'رابط جودة 1080p (اختياري)',
            hintText: 'https://drive.google.com/file/d/...',
            prefixIcon: Icon(Icons.video_file),
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value != null && value.trim().isNotEmpty) {
              if (!value.contains('drive.google.com')) {
                return 'يرجى إدخال رابط Google Drive صحيح';
              }
            }
            return null;
          },
        ),
      ],
    );
  }
}
