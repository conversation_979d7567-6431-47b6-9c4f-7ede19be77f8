import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/services/content_service.dart';
import '../../../../shared/services/section_service.dart';
import '../../../../core/models/section.dart';
import 'units_lessons_management_page.dart';

class SubjectsManagementPage extends StatefulWidget {
  const SubjectsManagementPage({super.key});

  @override
  State<SubjectsManagementPage> createState() => _SubjectsManagementPageState();
}

class _SubjectsManagementPageState extends State<SubjectsManagementPage> {
  List<Subject> _subjects = [];
  List<Section> _sections = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      // تحميل الأقسام والمواد معاً - للأدمن نحمل من Firebase مباشرة
      _sections = await SectionService.instance.loadSectionsFromFirebase();
      _subjects = await ContentService.instance.loadSubjectsFromFirebase();

      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل البيانات: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'إدارة المواد',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
        ),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showAddSubjectDialog,
            icon: const Icon(Icons.add),
            tooltip: 'إضافة مادة جديدة',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _subjects.isEmpty
          ? _buildEmptyState()
          : RefreshIndicator(
              onRefresh: _loadData,
              child: ListView.builder(
                padding: EdgeInsets.all(16.w),
                itemCount: _subjects.length,
                itemBuilder: (context, index) {
                  final subject = _subjects[index];
                  return _buildSubjectCard(subject);
                },
              ),
            ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.book_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد مواد',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'اضغط على + لإضافة مادة جديدة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubjectCard(Subject subject) {
    final color = Color(int.parse(subject.color.replaceFirst('#', '0xFF')));
    final section = _sections.firstWhere(
      (s) => s.id == subject.sectionId,
      orElse: () => Section(
        id: '',
        name: 'غير محدد',
        description: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            colors: [
              color.withValues(alpha: 0.1),
              color.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // أيقونة المادة
                  Container(
                    width: 50.w,
                    height: 50.h,
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Icon(Icons.book, color: Colors.white, size: 24.sp),
                  ),

                  SizedBox(width: 16.w),

                  // معلومات المادة
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          subject.name,
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          'القسم: ${section.name}',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: AppTheme.primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          subject.description,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: AppTheme.textSecondaryColor),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),

                  // مؤشرات الحالة
                  Column(
                    children: [
                      // نوع المادة
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: subject.isFree
                              ? Colors.orange.withValues(alpha: 0.1)
                              : AppTheme.primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Text(
                          subject.isFree ? 'مجانية' : 'مدفوعة',
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: subject.isFree
                                ? Colors.orange
                                : AppTheme.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),

                      SizedBox(height: 4.h),

                      // حالة التفعيل
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: subject.isActive
                              ? AppTheme.successColor.withValues(alpha: 0.1)
                              : AppTheme.textSecondaryColor.withValues(
                                  alpha: 0.1,
                                ),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Text(
                          subject.isActive ? 'مفعل' : 'معطل',
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: subject.isActive
                                ? AppTheme.successColor
                                : AppTheme.textSecondaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              SizedBox(height: 16.h),

              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                UnitsLessonsManagementPage(subject: subject),
                          ),
                        );
                      },
                      icon: Icon(Icons.folder, size: 16.sp),
                      label: Text('الوحدات والدروس'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: color.withValues(alpha: 0.1),
                        foregroundColor: color,
                        elevation: 0,
                        padding: EdgeInsets.symmetric(vertical: 8.h),
                      ),
                    ),
                  ),

                  SizedBox(width: 8.w),

                  IconButton(
                    onPressed: () => _showEditSubjectDialog(subject),
                    icon: Icon(Icons.edit, color: AppTheme.primaryColor),
                    tooltip: 'تعديل',
                  ),

                  IconButton(
                    onPressed: () => _toggleSubjectStatus(subject),
                    icon: Icon(
                      subject.isActive
                          ? Icons.visibility_off
                          : Icons.visibility,
                      color: subject.isActive
                          ? AppTheme.warningColor
                          : AppTheme.successColor,
                    ),
                    tooltip: subject.isActive ? 'إلغاء تفعيل' : 'تفعيل',
                  ),

                  IconButton(
                    onPressed: () => _deleteSubject(subject),
                    icon: Icon(Icons.delete, color: AppTheme.errorColor),
                    tooltip: 'حذف',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAddSubjectDialog() {
    _showSubjectDialog();
  }

  void _showEditSubjectDialog(Subject subject) {
    _showSubjectDialog(subject: subject);
  }

  void _showSubjectDialog({Subject? subject}) {
    final isEditing = subject != null;
    final nameController = TextEditingController(text: subject?.name ?? '');
    final descriptionController = TextEditingController(
      text: subject?.description ?? '',
    );
    Color selectedColor = subject != null
        ? Color(int.parse(subject.color.replaceFirst('#', '0xFF')))
        : AppTheme.primaryColor;
    String? selectedSectionId = subject?.sectionId.isNotEmpty == true
        ? subject?.sectionId
        : null;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(isEditing ? 'تعديل المادة' : 'إضافة مادة جديدة'),
          content: SingleChildScrollView(
            child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'اسم المادة',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  TextField(
                    controller: descriptionController,
                    maxLines: 3,
                    decoration: const InputDecoration(
                      labelText: 'وصف المادة',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  SizedBox(height: 16.h),

                  // اختيار القسم
                  DropdownButtonFormField<String>(
                    value: selectedSectionId,
                    decoration: const InputDecoration(
                      labelText: 'القسم',
                      border: OutlineInputBorder(),
                    ),
                    hint: const Text('اختر القسم'),
                    items: _sections.map((section) {
                      return DropdownMenuItem<String>(
                        value: section.id,
                        child: Text(
                          section.name,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(fontSize: 14.sp),
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setDialogState(() {
                        selectedSectionId = value;
                      });
                    },
                  ),
                  SizedBox(height: 8.h),
                  if (selectedSectionId != null)
                    Container(
                      padding: EdgeInsets.all(12.w),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Colors.blue.shade600,
                            size: 16,
                          ),
                          SizedBox(width: 8.w),
                          Expanded(
                            child: Text(
                              _sections
                                      .firstWhere(
                                        (s) => s.id == selectedSectionId,
                                      )
                                      .isFree
                                  ? 'هذه المادة ستكون متاحة مجاناً لجميع الطلاب'
                                  : 'هذه المادة ستتطلب اشتراك للوصول إليها',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.blue.shade700,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                  SizedBox(height: 16.h),
                  Text(
                    'اختر لون المادة:',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  SizedBox(height: 8.h),
                  Wrap(
                    spacing: 8.w,
                    children:
                        [
                              AppTheme.primaryColor,
                              AppTheme.secondaryColor,
                              AppTheme.accentColor,
                              AppTheme.successColor,
                              AppTheme.warningColor,
                              AppTheme.errorColor,
                            ]
                            .map(
                              (color) => GestureDetector(
                                onTap: () {
                                  setDialogState(() {
                                    selectedColor = color;
                                  });
                                },
                                child: Container(
                                  width: 40.w,
                                  height: 40.h,
                                  decoration: BoxDecoration(
                                    color: color,
                                    shape: BoxShape.circle,
                                    border: selectedColor == color
                                        ? Border.all(
                                            color: Colors.black,
                                            width: 3,
                                          )
                                        : null,
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('يرجى إدخال اسم المادة')),
                  );
                  return;
                }

                if (selectedSectionId == null || selectedSectionId!.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('يرجى اختيار القسم')),
                  );
                  return;
                }

                try {
                  final colorHex =
                      '#${selectedColor.toARGB32().toRadixString(16).substring(2)}';

                  // تحديد نوع المادة بناءً على القسم المختار
                  final selectedSection = _sections.firstWhere(
                    (s) => s.id == selectedSectionId,
                  );
                  final isFree = selectedSection.isFree;

                  if (isEditing) {
                    final updatedSubject = subject.copyWith(
                      name: nameController.text.trim(),
                      description: descriptionController.text.trim(),
                      color: colorHex,
                      isFree: isFree,
                      sectionId: selectedSectionId!,
                    );
                    debugPrint(
                      'تحديث المادة: ${updatedSubject.name}, مجانية: ${updatedSubject.isFree}',
                    );
                    await ContentService.instance.updateSubject(updatedSubject);
                  } else {
                    final newSubject = Subject(
                      id: DateTime.now().millisecondsSinceEpoch.toString(),
                      name: nameController.text.trim(),
                      description: descriptionController.text.trim(),
                      iconUrl: '',
                      color: colorHex,
                      isActive: true,
                      isFree: isFree,
                      sectionId: selectedSectionId!,
                      createdAt: DateTime.now(),
                      updatedAt: DateTime.now(),
                    );
                    debugPrint(
                      'إنشاء مادة جديدة: ${newSubject.name}, مجانية: ${newSubject.isFree}',
                    );
                    await ContentService.instance.addSubject(newSubject);
                  }

                  if (mounted && context.mounted) {
                    Navigator.pop(context);
                    _loadData();

                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          isEditing
                              ? 'تم تحديث المادة بنجاح'
                              : 'تم إضافة المادة بنجاح',
                        ),
                        backgroundColor: AppTheme.successColor,
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted && context.mounted) {
                    ScaffoldMessenger.of(
                      context,
                    ).showSnackBar(SnackBar(content: Text('خطأ: $e')));
                  }
                }
              },
              child: Text(isEditing ? 'تحديث' : 'إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _toggleSubjectStatus(Subject subject) async {
    try {
      final updatedSubject = subject.copyWith(isActive: !subject.isActive);
      await ContentService.instance.updateSubject(updatedSubject);
      _loadData();

      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              subject.isActive ? 'تم إلغاء تفعيل المادة' : 'تم تفعيل المادة',
            ),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ: $e')));
      }
    }
  }

  Future<void> _deleteSubject(Subject subject) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف مادة "${subject.name}"؟\nسيتم حذف جميع الوحدات والدروس والأسئلة المرتبطة بها.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ContentService.instance.deleteSubject(subject.id);
        _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف المادة بنجاح'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('خطأ في الحذف: $e')));
        }
      }
    }
  }
}
