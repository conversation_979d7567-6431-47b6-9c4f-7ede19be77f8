import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/models/section.dart';
import '../../../../shared/services/section_service.dart';

import 'subjects_by_section_page.dart';
import 'free_sections_page.dart';

class SectionsPage extends StatefulWidget {
  const SectionsPage({super.key});

  @override
  State<SectionsPage> createState() => _SectionsPageState();
}

class _SectionsPageState extends State<SectionsPage> {
  List<Section> _sections = [];
  bool _isLoading =
      true; // ابدأ بالتحميل لمنع ظهور "لا توجد أقسام" قبل جلب البيانات المحفوظة

  @override
  void initState() {
    super.initState();
    debugPrint('🔥 بدء تحميل صفحة الأقسام...');
    _loadSections();
  }

  Future<void> _loadSections() async {
    try {
      // 🎯 عرض الأقسام الحقيقية المحفوظة مباشرة (مثل VideoService)

      // الحصول على الأقسام المحفوظة مباشرة (مثل VideoService.getPaidVideoSections)
      final savedSections = await SectionService.instance.getPaidSections();

      if (savedSections.isNotEmpty) {
        setState(() {
          _sections = savedSections;
          _isLoading = false;
        });
        debugPrint('🚀 تم عرض ${_sections.length} قسم حقيقي مباشرة من الكاش!');

        // تحديث البيانات في الخلفية بصمت
        _updateFromNetworkSilently();
      } else {
        setState(() => _isLoading = false);
        debugPrint('📱 لا توجد أقسام محفوظة');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الأقسام: $e');
      setState(() => _isLoading = false);
    }
  }

  /// تحديث البيانات في الخلفية بدون تأثير على العرض
  Future<void> _updateFromNetworkSilently() async {
    try {
      debugPrint('🔄 تحديث الأقسام في الخلفية...');

      // محاولة تحميل الأقسام المحفوظة أولاً
      final savedSections = await SectionService.instance.getPaidSections();
      if (savedSections.isNotEmpty && mounted) {
        _sections = savedSections;
        setState(() {});
        debugPrint('✅ تم تحديث ${_sections.length} قسم من البيانات المحفوظة');
      }

      // ثم محاولة التحديث من Firebase في الخلفية
      final success = await SectionService.instance
          .updateSectionsFromFirebase();

      if (success && mounted) {
        _sections = SectionService.instance.paidSections;
        if (mounted) {
          setState(() {});
          debugPrint(
            '✅ تم تحديث ${_sections.length} قسم من Firebase في الخلفية',
          );
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحديث في الخلفية: $e');
    }
  }

  /// تحديث يدوي للأقسام (Pull-to-Refresh)
  Future<void> _refreshSections() async {
    try {
      debugPrint('🔄 تحديث يدوي للأقسام...');
      await _updateFromNetworkSilently();
    } catch (e) {
      debugPrint('❌ خطأ في التحديث اليدوي للأقسام: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحديث الأقسام: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'الاختبارات',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
        ),
        actions: [
          // زر تجربة التطبيق مع شعار الاختبارات
          Container(
            margin: EdgeInsets.only(left: 8.w),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(20.r),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const FreeSectionsPage(),
                    ),
                  );
                },
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20.r),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.quiz_outlined,
                        color: Colors.white,
                        size: 20.sp,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        'تجربة التطبيق',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _sections.isEmpty
          ? _buildEmptyState()
          : RefreshIndicator(
              onRefresh: _refreshSections,
              child: Builder(
                builder: (context) {
                  final isLandscape =
                      MediaQuery.of(context).orientation ==
                      Orientation.landscape;
                  if (isLandscape) {
                    // في الوضع الأفقي: نستخدم SingleChildScrollView مع تخطيط مضغوط
                    return SingleChildScrollView(
                      padding: EdgeInsets.all(16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildCompactHeader(),
                          SizedBox(height: 16.h),
                          GridView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2,
                                  crossAxisSpacing: 16.w,
                                  mainAxisSpacing: 16.h,
                                  childAspectRatio: 1.1,
                                ),
                            itemCount: _sections.length,
                            itemBuilder: (context, index) {
                              final section = _sections[index];
                              return _buildSectionCard(section);
                            },
                          ),
                        ],
                      ),
                    );
                  } else {
                    // في الوضع العمودي: نستخدم التخطيط العادي
                    return Padding(
                      padding: EdgeInsets.all(16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildHeader(),
                          SizedBox(height: 20.h),
                          Expanded(
                            child: GridView.builder(
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 2,
                                    crossAxisSpacing: 16.w,
                                    mainAxisSpacing: 16.h,
                                    childAspectRatio: 1.1,
                                  ),
                              itemCount: _sections.length,
                              itemBuilder: (context, index) {
                                final section = _sections[index];
                                return _buildSectionCard(section);
                              },
                            ),
                          ),
                        ],
                      ),
                    );
                  }
                },
              ),
            ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(Icons.school, color: Colors.white, size: 24.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مكتبة الاختبارات',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'اختر القسم لحل الاختبارات التعليمية',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(Icons.school, color: Colors.white, size: 20.sp),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              'مكتبة الاختبارات - اختر القسم لحل الاختبارات التعليمية',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(Section section) {
    return GestureDetector(
      onTap: () => _navigateToSubjects(section),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: _getSectionColor(section.color).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(
                Icons.school,
                color: _getSectionColor(section.color),
                size: 32.sp,
              ),
            ),
            SizedBox(height: 12.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.w),
              child: Text(
                section.name,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.school_outlined, size: 80.w, color: Colors.grey[400]),
          SizedBox(height: 16.h),
          Text(
            'لا توجد أقسام متاحة',
            style: TextStyle(
              fontSize: 18.sp,
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة الأقسام قريباً',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  void _navigateToSubjects(Section section) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SubjectsBySectionPage(section: section),
      ),
    );
  }

  Color _getSectionColor(String colorString) {
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return AppTheme.primaryColor; // لون افتراضي في حالة الخطأ
    }
  }
}
