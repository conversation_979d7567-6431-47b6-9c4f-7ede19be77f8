import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/models/section.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/services/content_service.dart';
import '../../../../shared/services/subscription_service.dart';
import '../../../../shared/services/persistent_storage_service.dart';

import 'subject_detail_page.dart';
import 'question_id_search_page.dart';

class SubjectsBySectionPage extends StatefulWidget {
  final Section section;
  final bool isFreeAccess; // هل الوصول مجاني (للأقسام المجانية)

  const SubjectsBySectionPage({
    super.key,
    required this.section,
    this.isFreeAccess = false,
  });

  @override
  State<SubjectsBySectionPage> createState() => _SubjectsBySectionPageState();
}

class _SubjectsBySectionPageState extends State<SubjectsBySectionPage> {
  List<Subject> _subjects = [];

  @override
  void initState() {
    super.initState();

    // تحميل البيانات فوراً من الذاكرة (بدون await)
    _loadDataImmediately();

    // الاستماع لتغييرات SubscriptionService
    SubscriptionService.instance.addListener(_onSubscriptionChanged);
  }

  /// تحميل البيانات فوراً من الذاكرة الدائمة (بدون شبكة)
  Future<void> _loadDataImmediately() async {
    try {
      // جلب البيانات مباشرة من الذاكرة الدائمة (بدون أي اعتماد على الشبكة)
      final persistentSubjects = await PersistentStorageService.instance
          .loadSubjects();

      debugPrint(
        '🔍 جميع المواد في الذاكرة الدائمة (${persistentSubjects.length}):',
      );
      for (var subject in persistentSubjects) {
        debugPrint(
          '   - ${subject.name} (sectionId: "${subject.sectionId}", isActive: ${subject.isActive})',
        );
      }

      // تصفية المواد حسب القسم والحالة النشطة
      final subjects = persistentSubjects
          .where((s) => s.isActive && s.sectionId == widget.section.id)
          .toList();

      debugPrint(
        '🔍 المواد للقسم ${widget.section.id}: ${subjects.map((s) => s.name).join(', ')}',
      );

      // عرض البيانات فوراً
      setState(() {
        _subjects = subjects;
      });

      debugPrint('🚀 تم عرض ${subjects.length} مادة فوراً من الذاكرة الدائمة');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل المواد من الذاكرة الدائمة: $e');
      // في حالة الخطأ، استخدم قائمة فارغة
      setState(() {
        _subjects = [];
      });
    }

    // تحديث البيانات في الخلفية (بدون تأثير على العرض)
    _updateInBackground();
  }

  @override
  void dispose() {
    SubscriptionService.instance.removeListener(_onSubscriptionChanged);
    super.dispose();
  }

  void _onSubscriptionChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  /// تحديث البيانات في الخلفية (بدون مؤشر تحميل)
  Future<void> _updateInBackground() async {
    try {
      debugPrint('🔄 تحديث المواد في الخلفية...');

      // تحديث البيانات من Firebase
      final updatedSubjects = await ContentService.instance
          .forceReloadSubjects();

      // حفظ البيانات المحدثة في الذاكرة الدائمة
      await PersistentStorageService.instance.saveSubjects(updatedSubjects);
      debugPrint(
        '💾 تم حفظ ${updatedSubjects.length} مادة محدثة في الذاكرة الدائمة',
      );

      // تصفية المواد للقسم المحدد
      final sectionSubjects = updatedSubjects
          .where((s) => s.sectionId == widget.section.id && s.isActive)
          .toList();

      // تحديث الواجهة بصمت
      if (mounted) {
        setState(() {
          _subjects = sectionSubjects;
        });
        debugPrint('✅ تم تحديث ${sectionSubjects.length} مادة في الخلفية');
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحديث في الخلفية: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          widget.section.name,
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          // زر تحديث جميع المواد
          IconButton(
            icon: Icon(Icons.refresh, color: Colors.white),
            onPressed: () {
              // يمكن إضافة منطق تحديث جميع المواد هنا لاحقاً
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('سيتم إضافة تحديث المواد قريباً')),
              );
            },
            tooltip: 'تحديث جميع المواد',
          ),
          // زر البحث بـ ID السؤال
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const QuestionIdSearchPage(),
                ),
              );
            },
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    // إذا كان هناك بيانات، نعرضها فور<|im_start|> (مثل صفحة الفيديوهات)
    if (_subjects.isNotEmpty) {
      return _buildSubjectsContent();
    }

    // التحقق من وجود مواد في الذاكرة الدائمة قبل عرض "لا توجد مواد"
    return FutureBuilder<bool>(
      future: _hasSubjectsInPersistentStorage(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          // أثناء التحقق، لا نعرض شيئاً (تجنب الوميض)
          return const SizedBox.shrink();
        }

        if (snapshot.data == true) {
          // توجد مواد في الكاش، لا نعرض "لا توجد مواد"
          return const SizedBox.shrink();
        }

        // لا توجد مواد في الكاش، نعرض الرسالة
        return _buildEmptyState();
      },
    );
  }

  /// التحقق من وجود مواد في الذاكرة الدائمة للقسم الحالي
  Future<bool> _hasSubjectsInPersistentStorage() async {
    try {
      final persistentSubjects = await PersistentStorageService.instance
          .loadSubjects();
      final subjects = persistentSubjects
          .where((s) => s.isActive && s.sectionId == widget.section.id)
          .toList();
      return subjects.isNotEmpty;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الذاكرة الدائمة: $e');
      return false;
    }
  }

  Widget _buildSubjectsContent() {
    return RefreshIndicator(
      onRefresh: () async {
        debugPrint('🔄 تحديث يدوي للمواد...');
        await _updateInBackground();
      },
      child: GridView.builder(
        padding: EdgeInsets.all(16.w),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16.w,
          mainAxisSpacing: 16.h,
          childAspectRatio: 0.85,
        ),
        itemCount: _subjects.length,
        itemBuilder: (context, index) {
          final subject = _subjects[index];
          return _buildSubjectCard(subject);
        },
      ),
    );
  }

  Widget _buildSubjectCard(Subject subject) {
    final isSubscribed = SubscriptionService.instance.isSubscribedToSubject(
      subject.id,
    );
    // إذا كان القسم مجاني أو المادة مجانية، فالوصول متاح
    final isFreeAccess = widget.isFreeAccess || subject.isFree;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20.r),
          onTap: () => _navigateToSubject(subject, isFreeAccess),
          child: Padding(
            padding: EdgeInsets.all(20.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة المادة
                Container(
                  width: 60.w,
                  height: 60.w,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color(
                          int.parse(subject.color.replaceFirst('#', '0xFF')),
                        ),
                        Color(
                          int.parse(subject.color.replaceFirst('#', '0xFF')),
                        ).withValues(alpha: 0.7),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(50.r),
                  ),
                  child: Icon(Icons.book, color: Colors.white, size: 30.w),
                ),

                SizedBox(height: 16.h),

                // اسم المادة
                Text(
                  subject.name,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                SizedBox(height: 12.h),

                // حالة الاشتراك
                if (isFreeAccess)
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 6.h,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      'متاح',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.green,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  )
                else if (isSubscribed)
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 6.h,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      'متاح',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.green,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  )
                else
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 6.h,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      'مقفل',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.orange,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.book_outlined, size: 80.w, color: Colors.grey[400]),
          SizedBox(height: 16.h),
          Text(
            'لا توجد مواد في هذا القسم',
            style: TextStyle(
              fontSize: 18.sp,
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة المواد قريباً',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  void _navigateToSubject(Subject subject, bool isFreeAccess) {
    final isSubscribed = SubscriptionService.instance.isSubscribedToSubject(
      subject.id,
    );

    // التحقق من إمكانية الوصول
    // إذا كان القسم مجاني أو المادة مجانية، فالوصول متاح
    final hasAccess = widget.isFreeAccess || subject.isFree || isSubscribed;

    if (!hasAccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب تفعيل الاشتراك للوصول إلى هذه المادة'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SubjectDetailPage(
          subject: subject,
          isFreeAccess: widget.isFreeAccess || subject.isFree,
        ),
      ),
    );
  }
}
