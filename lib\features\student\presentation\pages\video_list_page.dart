import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/services/subscription_service.dart';
import '../../../../shared/services/video_download_service.dart';
import '../widgets/modern_video_player.dart';

/// صفحة قائمة الفيديوهات للطالب - مُعاد كتابتها بالكامل مع مشغل فيديو متطور
class VideoListPage extends StatefulWidget {
  final String lessonId;
  final bool hasSubscription;

  const VideoListPage({
    super.key,
    required this.lessonId,
    required this.hasSubscription,
  });

  @override
  State<VideoListPage> createState() => _VideoListPageState();
}

class _VideoListPageState extends State<VideoListPage> {
  final VideoService _videoService = VideoService.instance;
  final SubscriptionService _subscriptionService = SubscriptionService.instance;
  final VideoDownloadService _downloadService = VideoDownloadService.instance;

  List<Video> _videos = [];
  bool _isNetworkLoading = false;
  bool _isRefreshing = false;
  bool _hasTriedLoading = false;
  Map<String, double> _downloadProgress = {};
  Map<String, bool> _isDownloading = {};

  @override
  void initState() {
    super.initState();
    _loadDataImmediately();
  }

  /// تحميل البيانات فوراً بدون انتظار
  void _loadDataImmediately() {
    // محاولة تحميل البيانات المحفوظة أولاً
    _loadSavedDataFirst();
  }

  /// تحميل البيانات المحفوظة أولاً
  Future<void> _loadSavedDataFirst() async {
    try {
      // فحص البيانات المحلية من الكاش أولاً (بدون await)
      final cachedVideos = _videoService.getCachedVideos(widget.lessonId);
      final activeCachedVideos = cachedVideos
          .where((video) => video.isActive)
          .toList();

      if (activeCachedVideos.isNotEmpty) {
        // البيانات متوفرة في الكاش - عرضها فوراً
        setState(() {
          _videos = activeCachedVideos;
          _hasTriedLoading = true;
        });
        debugPrint(
          '⚡ إرجاع ${activeCachedVideos.length} فيديو من الكاش للدرس ${widget.lessonId}',
        );
        debugPrint(
          '🚀 تم عرض ${activeCachedVideos.length} فيديو من البيانات المحفوظة',
        );

        // تحميل بيانات الاشتراك في الخلفية
        _loadSubscriptionInBackground();

        // بدء التحديث في الخلفية
        _updateFromNetworkSilently();
        return;
      }

      // إذا لم توجد بيانات في الكاش، حاول تحميل البيانات من Firebase
      debugPrint('📱 لا توجد فيديوهات في الكاش، محاولة تحميل من Firebase...');

      // تحميل بيانات الاشتراك في الخلفية
      _loadSubscriptionInBackground();

      // محاولة تحميل البيانات من Firebase
      try {
        final savedVideos = await _videoService.getVideos(widget.lessonId);
        final activeSavedVideos = savedVideos
            .where((video) => video.isActive)
            .toList();

        setState(() {
          _videos = activeSavedVideos;
          _hasTriedLoading = true;
        });

        if (activeSavedVideos.isNotEmpty) {
          debugPrint(
            '✅ تم تحميل ${activeSavedVideos.length} فيديو من Firebase',
          );
        } else {
          debugPrint('📱 لا توجد فيديوهات نشطة للدرس ${widget.lessonId}');
        }
      } catch (e) {
        debugPrint('❌ خطأ في تحميل البيانات من Firebase: $e');
        setState(() {
          _hasTriedLoading = true;
        });
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات: $e');
      setState(() {
        _hasTriedLoading = true;
      });

      // تحميل بيانات الاشتراك في الخلفية
      _loadSubscriptionInBackground();
    }
  }

  /// تحميل بيانات الاشتراك في الخلفية
  Future<void> _loadSubscriptionInBackground() async {
    try {
      await _subscriptionService.loadUserSubscription();
      // تحديث صامت من الشبكة في الخلفية
      _updateFromNetworkSilently();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات الاشتراك: $e');
    }
  }

  /// تحديث صامت من الشبكة في الخلفية
  Future<void> _updateFromNetworkSilently() async {
    try {
      final hasInternet = await _checkInternetConnection();
      if (!hasInternet) {
        debugPrint('📱 لا توجد شبكة - الاعتماد على البيانات المحلية فقط');
        return;
      }

      debugPrint('🌐 تحديث الفيديوهات من الشبكة في الخلفية...');
      // تحديث الفيديوهات من Firebase باستخدام الدالة الجديدة
      final updatedVideos = await _videoService.refreshVideosFromFirebase(
        widget.lessonId,
      );

      if (mounted && updatedVideos.isNotEmpty) {
        final activeUpdatedVideos = updatedVideos
            .where((video) => video.isActive)
            .toList();
        setState(() {
          _videos = activeUpdatedVideos;
        });
        debugPrint('🔄 تم تحديث ${activeUpdatedVideos.length} فيديو من الشبكة');
      }
    } catch (e) {
      debugPrint(
        '⚠️ فشل التحديث من الشبكة (لا مشكلة - البيانات المحلية متوفرة): $e',
      );
    }
  }

  /// فحص الاتصال بالإنترنت
  Future<bool> _checkInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// تحديث يدوي (Pull-to-Refresh)
  Future<void> _refreshData() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      debugPrint('🔄 تحديث يدوي للفيديوهات...');
      final videos = await _videoService.refreshVideosFromFirebase(
        widget.lessonId,
      );
      final activeVideos = videos.where((video) => video.isActive).toList();

      if (mounted) {
        setState(() {
          _videos = activeVideos;
          _isRefreshing = false;
        });
        debugPrint('✅ تم تحديث ${activeVideos.length} فيديو يدوياً');
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحديث اليدوي: $e');
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في التحديث: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'الفيديوهات',
        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: Container(
        decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
      ),
      foregroundColor: Colors.white,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _isRefreshing ? null : _refreshData,
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  Widget _buildBody() {
    // إذا كان هناك بيانات، نعرضها فوراً
    if (_videos.isNotEmpty) {
      return _buildVideosContent();
    }

    // إذا كان يحمل من الشبكة، نعرض مؤشر التحميل
    if (_isNetworkLoading) {
      return _buildLoadingIndicator();
    }

    // إذا لم نحاول التحميل بعد، نعرض مؤشر التحميل
    if (!_hasTriedLoading) {
      return _buildLoadingIndicator();
    }

    // فقط إذا حاولنا التحميل ولم توجد بيانات، نعرض رسالة فارغة
    return _buildEmptyState();
  }

  Widget _buildLoadingIndicator() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildVideosContent() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: 20.h),
            Expanded(child: _buildVideosList()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(Icons.video_library, color: Colors.white, size: 24.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'قائمة الفيديوهات',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  '${_videos.length} فيديو متاح',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideosList() {
    return ListView.builder(
      itemCount: _videos.length,
      itemBuilder: (context, index) {
        final video = _videos[index];
        return _buildVideoCard(video);
      },
    );
  }

  Widget _buildVideoCard(Video video) {
    final isDownloaded = _downloadService.isVideoDownloaded(video.id);
    final isDownloading = _isDownloading[video.id] ?? false;
    final downloadProgress = _downloadProgress[video.id] ?? 0.0;

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16.r),
          onTap: () => _playVideo(video),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    _buildVideoThumbnail(video, isDownloaded),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            video.title,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.textPrimaryColor,
                            ),
                          ),
                          if (video.description.isNotEmpty) ...[
                            SizedBox(height: 4.h),
                            Text(
                              video.description,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppTheme.textSecondaryColor,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                          SizedBox(height: 8.h),
                          _buildVideoInfo(video),
                        ],
                      ),
                    ),
                  ],
                ),
                if (isDownloading) ...[
                  SizedBox(height: 12.h),
                  _buildDownloadProgress(downloadProgress),
                ],
                SizedBox(height: 12.h),
                _buildVideoActions(video, isDownloaded, isDownloading),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVideoThumbnail(Video video, bool isDownloaded) {
    return Container(
      width: 80.w,
      height: 60.w,
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Icon(Icons.play_circle_filled, color: Colors.white, size: 32.sp),
          if (isDownloaded)
            Positioned(
              top: 4.h,
              right: 4.w,
              child: Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.download_done,
                  color: Colors.white,
                  size: 12.sp,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildVideoInfo(Video video) {
    return Row(
      children: [
        Icon(
          Icons.access_time,
          size: 14.sp,
          color: AppTheme.textSecondaryColor,
        ),
        SizedBox(width: 4.w),
        Text(
          _formatDuration(video.durationInSeconds),
          style: TextStyle(fontSize: 12.sp, color: AppTheme.textSecondaryColor),
        ),
        SizedBox(width: 16.w),
        Icon(Icons.storage, size: 14.sp, color: AppTheme.textSecondaryColor),
        SizedBox(width: 4.w),
        Text(
          _formatFileSize(video.fileSizeInBytes),
          style: TextStyle(fontSize: 12.sp, color: AppTheme.textSecondaryColor),
        ),
      ],
    );
  }

  Widget _buildDownloadProgress(double progress) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'جاري التحميل...',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '${(progress * 100).toInt()}%',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: 4.h),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
        ),
      ],
    );
  }

  Widget _buildVideoActions(
    Video video,
    bool isDownloaded,
    bool isDownloading,
  ) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _playVideo(video),
            icon: Icon(
              isDownloaded
                  ? Icons.play_circle_filled
                  : Icons.play_circle_outline,
              size: 20.sp,
            ),
            label: Text(
              isDownloaded ? 'تشغيل محلي' : 'تشغيل',
              style: TextStyle(fontSize: 14.sp),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
          ),
        ),
        SizedBox(width: 12.w),
        _buildDownloadButton(video, isDownloaded, isDownloading),
      ],
    );
  }

  Widget _buildDownloadButton(
    Video video,
    bool isDownloaded,
    bool isDownloading,
  ) {
    if (isDownloaded) {
      return IconButton(
        onPressed: () => _showDownloadedMessage(),
        icon: Icon(Icons.download_done, color: Colors.green, size: 24.sp),
        tooltip: 'تم التحميل',
      );
    }

    if (isDownloading) {
      return IconButton(
        onPressed: () => _cancelDownload(video),
        icon: Icon(Icons.cancel, color: Colors.red, size: 24.sp),
        tooltip: 'إلغاء التحميل',
      );
    }

    return PopupMenuButton<String>(
      onSelected: (quality) => _downloadVideo(video, quality),
      icon: Icon(Icons.download, color: AppTheme.primaryColor, size: 24.sp),
      tooltip: 'تحميل',
      itemBuilder: (context) => [
        const PopupMenuItem(value: '360p', child: Text('360p - جودة منخفضة')),
        const PopupMenuItem(value: '480p', child: Text('480p - جودة متوسطة')),
        const PopupMenuItem(value: '720p', child: Text('720p - جودة عالية')),
      ],
    );
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  void _playVideo(Video video) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ModernVideoPlayer(
          video: video,
          hasSubscription: widget.hasSubscription,
        ),
      ),
    );
  }

  Future<void> _downloadVideo(Video video, String quality) async {
    setState(() {
      _isDownloading[video.id] = true;
      _downloadProgress[video.id] = 0.0;
    });

    try {
      await _downloadService.downloadVideo(
        video,
        quality,
        onProgress: (progress) {
          if (mounted) {
            setState(() {
              _downloadProgress[video.id] = progress;
            });
          }
        },
      );

      if (mounted) {
        setState(() {
          _isDownloading[video.id] = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('تم تحميل الفيديو بنجاح')));
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isDownloading[video.id] = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الفيديو: $e')));
      }
    }
  }

  void _cancelDownload(Video video) {
    _downloadService.cancelDownload(video.id);
    setState(() {
      _isDownloading[video.id] = false;
      _downloadProgress[video.id] = 0.0;
    });
  }

  void _showDownloadedMessage() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('الفيديو محمل بالفعل')));
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_library_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد فيديوهات متاحة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة الفيديوهات قريباً',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
