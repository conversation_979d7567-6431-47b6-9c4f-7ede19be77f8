import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/services/subscription_service.dart';
import 'video_player_page.dart';

/// صفحة قائمة الفيديوهات للطالب - مُعاد كتابتها بالكامل مع مشغل فيديو متطور
class VideoListPage extends StatefulWidget {
  final String lessonId;
  final bool hasSubscription;

  const VideoListPage({
    super.key,
    required this.lessonId,
    required this.hasSubscription,
  });

  @override
  State<VideoListPage> createState() => _VideoListPageState();
}

class _VideoListPageState extends State<VideoListPage> {
  final VideoService _videoService = VideoService.instance;
  final SubscriptionService _subscriptionService = SubscriptionService.instance;

  List<Video> _videos = [];
  bool _isNetworkLoading = false;
  bool _isRefreshing = false;
  bool _hasTriedLoading = false;
  Map<String, double> _downloadProgress = {};
  Map<String, bool> _isDownloading = {};
  Map<String, int> _downloadedBytes = {};
  Map<String, int> _totalBytes = {};

  @override
  void initState() {
    super.initState();
    _loadDataImmediately();
    // تنظيف الملفات المؤقتة القديمة
    _videoService.cleanupTempFiles();
  }

  /// تحميل البيانات فوراً بدون انتظار
  void _loadDataImmediately() {
    // محاولة تحميل البيانات المحفوظة أولاً
    _loadSavedDataFirst();
  }

  /// تحميل البيانات المحفوظة أولاً
  Future<void> _loadSavedDataFirst() async {
    try {
      // فحص البيانات المحلية من الكاش أولاً (بدون await)
      final cachedVideos = _videoService.getCachedVideos(widget.lessonId);
      final activeCachedVideos = cachedVideos
          .where((video) => video.isActive)
          .toList();

      if (activeCachedVideos.isNotEmpty) {
        // البيانات متوفرة في الكاش - عرضها فوراً
        setState(() {
          _videos = activeCachedVideos;
          _hasTriedLoading = true;
        });
        debugPrint(
          '⚡ إرجاع ${activeCachedVideos.length} فيديو من الكاش للدرس ${widget.lessonId}',
        );
        debugPrint(
          '🚀 تم عرض ${activeCachedVideos.length} فيديو من البيانات المحفوظة',
        );

        // تحميل بيانات الاشتراك في الخلفية
        _loadSubscriptionInBackground();

        // بدء التحديث في الخلفية
        _updateFromNetworkSilently();
        return;
      }

      // إذا لم توجد بيانات في الكاش، حاول تحميل البيانات من Firebase
      debugPrint('📱 لا توجد فيديوهات في الكاش، محاولة تحميل من Firebase...');

      // تحميل بيانات الاشتراك في الخلفية
      _loadSubscriptionInBackground();

      // محاولة تحميل البيانات من Firebase
      try {
        final savedVideos = await _videoService.getVideos(widget.lessonId);
        final activeSavedVideos = savedVideos
            .where((video) => video.isActive)
            .toList();

        setState(() {
          _videos = activeSavedVideos;
          _hasTriedLoading = true;
        });

        if (activeSavedVideos.isNotEmpty) {
          debugPrint(
            '✅ تم تحميل ${activeSavedVideos.length} فيديو من Firebase',
          );
        } else {
          debugPrint('📱 لا توجد فيديوهات نشطة للدرس ${widget.lessonId}');
        }
      } catch (e) {
        debugPrint('❌ خطأ في تحميل البيانات من Firebase: $e');
        setState(() {
          _hasTriedLoading = true;
        });
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات: $e');
      setState(() {
        _hasTriedLoading = true;
      });

      // تحميل بيانات الاشتراك في الخلفية
      _loadSubscriptionInBackground();
    }
  }

  /// تحميل بيانات الاشتراك في الخلفية
  Future<void> _loadSubscriptionInBackground() async {
    try {
      await _subscriptionService.loadUserSubscription();
      // تحديث صامت من الشبكة في الخلفية
      _updateFromNetworkSilently();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات الاشتراك: $e');
    }
  }

  /// تحديث صامت من الشبكة في الخلفية
  Future<void> _updateFromNetworkSilently() async {
    try {
      final hasInternet = await _checkInternetConnection();
      if (!hasInternet) {
        debugPrint('📱 لا توجد شبكة - الاعتماد على البيانات المحلية فقط');
        return;
      }

      debugPrint('🌐 تحديث الفيديوهات من الشبكة في الخلفية...');
      // تحديث الفيديوهات من Firebase باستخدام الدالة الجديدة
      final updatedVideos = await _videoService.refreshVideosFromFirebase(
        widget.lessonId,
      );

      if (mounted && updatedVideos.isNotEmpty) {
        final activeUpdatedVideos = updatedVideos
            .where((video) => video.isActive)
            .toList();
        setState(() {
          _videos = activeUpdatedVideos;
        });
        debugPrint('🔄 تم تحديث ${activeUpdatedVideos.length} فيديو من الشبكة');
      }
    } catch (e) {
      debugPrint(
        '⚠️ فشل التحديث من الشبكة (لا مشكلة - البيانات المحلية متوفرة): $e',
      );
    }
  }

  /// فحص الاتصال بالإنترنت
  Future<bool> _checkInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// تحديث يدوي (Pull-to-Refresh)
  Future<void> _refreshData() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      debugPrint('🔄 تحديث يدوي للفيديوهات...');
      final videos = await _videoService.refreshVideosFromFirebase(
        widget.lessonId,
      );
      final activeVideos = videos.where((video) => video.isActive).toList();

      if (mounted) {
        setState(() {
          _videos = activeVideos;
          _isRefreshing = false;
        });
        debugPrint('✅ تم تحديث ${activeVideos.length} فيديو يدوياً');
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحديث اليدوي: $e');
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في التحديث: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'الفيديوهات',
        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: Container(
        decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
      ),
      foregroundColor: Colors.white,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _isRefreshing ? null : _refreshData,
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  Widget _buildBody() {
    // إذا كان هناك بيانات، نعرضها فوراً
    if (_videos.isNotEmpty) {
      return _buildVideosContent();
    }

    // إذا كان يحمل من الشبكة، نعرض مؤشر التحميل
    if (_isNetworkLoading) {
      return _buildLoadingIndicator();
    }

    // إذا لم نحاول التحميل بعد، نعرض مؤشر التحميل
    if (!_hasTriedLoading) {
      return _buildLoadingIndicator();
    }

    // فقط إذا حاولنا التحميل ولم توجد بيانات، نعرض رسالة فارغة
    return _buildEmptyState();
  }

  Widget _buildLoadingIndicator() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildVideosContent() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: 20.h),
            Expanded(child: _buildVideosList()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(Icons.video_library, color: Colors.white, size: 24.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'قائمة الفيديوهات',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  '${_videos.length} فيديو متاح',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideosList() {
    return ListView.builder(
      itemCount: _videos.length,
      itemBuilder: (context, index) {
        final video = _videos[index];
        return _buildVideoCard(video);
      },
    );
  }

  Widget _buildVideoCard(Video video) {
    final isDownloading = _isDownloading[video.id] ?? false;
    final downloadProgress = _downloadProgress[video.id] ?? 0.0;

    return FutureBuilder<List<VideoQuality>>(
      future: _videoService.getDownloadedQualities(video.id),
      builder: (context, snapshot) {
        final downloadedQualities = snapshot.data ?? [];
        final isDownloaded = downloadedQualities.isNotEmpty;

        return Container(
          margin: EdgeInsets.only(bottom: 16.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(16.r),
              onTap: () => _handlePlayVideo(video, isDownloaded),
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        _buildVideoThumbnail(video, isDownloaded),
                        SizedBox(width: 16.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                video.title,
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.textPrimaryColor,
                                ),
                              ),
                              if (video.description.isNotEmpty) ...[
                                SizedBox(height: 4.h),
                                Text(
                                  video.description,
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: AppTheme.textSecondaryColor,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                              SizedBox(height: 8.h),
                              _buildVideoInfo(video),
                              if (isDownloaded) ...[
                                SizedBox(height: 4.h),
                                _buildDownloadedInfo(downloadedQualities),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                    if (isDownloading) ...[
                      SizedBox(height: 12.h),
                      _buildDownloadProgress(downloadProgress, video.id),
                    ],
                    SizedBox(height: 12.h),
                    _buildVideoActions(video, isDownloaded, isDownloading),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildVideoThumbnail(Video video, bool isDownloaded) {
    return GestureDetector(
      onTap: () => _handlePlayVideo(video, isDownloaded),
      child: Container(
        width: 80.w,
        height: 60.w,
        decoration: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Icon(Icons.play_circle_filled, color: Colors.white, size: 32.sp),
            if (isDownloaded)
              Positioned(
                top: 4.h,
                right: 4.w,
                child: Container(
                  padding: EdgeInsets.all(2.w),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    Icons.download_done,
                    color: Colors.white,
                    size: 12.sp,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoInfo(Video video) {
    return Row(
      children: [
        Icon(
          Icons.access_time,
          size: 14.sp,
          color: AppTheme.textSecondaryColor,
        ),
        SizedBox(width: 4.w),
        Text(
          _formatDuration(video.durationInSeconds),
          style: TextStyle(fontSize: 12.sp, color: AppTheme.textSecondaryColor),
        ),
      ],
    );
  }

  /// بناء معلومات الجودات المحملة
  Widget _buildDownloadedInfo(List<VideoQuality> downloadedQualities) {
    return Row(
      children: [
        Icon(Icons.download_done, size: 14.sp, color: Colors.green),
        SizedBox(width: 4.w),
        Text(
          'محمل: ${downloadedQualities.map((q) => q.name.replaceAll('quality', '')).join(', ')}',
          style: TextStyle(
            fontSize: 12.sp,
            color: Colors.green,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildDownloadProgress(double progress, String videoId) {
    final downloadedBytes = _downloadedBytes[videoId] ?? 0;
    final totalBytes = _totalBytes[videoId] ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'جاري التحميل...',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '${(progress * 100).toInt()}%',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        if (totalBytes > 0) ...[
          SizedBox(height: 2.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatFileSize(downloadedBytes),
                style: TextStyle(
                  fontSize: 10.sp,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              Text(
                _formatFileSize(totalBytes),
                style: TextStyle(
                  fontSize: 10.sp,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ],
        SizedBox(height: 4.h),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
        ),
      ],
    );
  }

  Widget _buildVideoActions(
    Video video,
    bool isDownloaded,
    bool isDownloading,
  ) {
    return Row(
      children: [
        _buildDownloadButton(video, isDownloaded, isDownloading),
        if (isDownloaded) ...[SizedBox(width: 8.w), _buildDeleteButton(video)],
      ],
    );
  }

  Widget _buildDownloadButton(
    Video video,
    bool isDownloaded,
    bool isDownloading,
  ) {
    if (isDownloaded) {
      return IconButton(
        onPressed: () => _showDownloadedMessage(),
        icon: Icon(Icons.download_done, color: Colors.green, size: 24.sp),
        tooltip: 'تم التحميل',
      );
    }

    if (isDownloading) {
      return IconButton(
        onPressed: () => _cancelDownload(video),
        icon: Icon(Icons.cancel, color: Colors.red, size: 24.sp),
        tooltip: 'إلغاء التحميل',
      );
    }

    return PopupMenuButton<String>(
      onSelected: (quality) => _downloadVideo(video, quality),
      icon: Icon(Icons.download, color: AppTheme.primaryColor, size: 24.sp),
      tooltip: 'تحميل',
      itemBuilder: (context) => _buildAvailableQualityItems(video),
    );
  }

  /// بناء قائمة الجودات المتاحة للتحميل
  List<PopupMenuEntry<String>> _buildAvailableQualityItems(Video video) {
    final availableQualities = video.availableQualities;
    final List<PopupMenuEntry<String>> items = [];

    for (final quality in availableQualities) {
      String qualityText;
      String description;

      switch (quality) {
        case VideoQuality.quality360:
          qualityText = '360p';
          description = 'جودة منخفضة';
          break;
        case VideoQuality.quality480:
          qualityText = '480p';
          description = 'جودة متوسطة';
          break;
        case VideoQuality.quality720:
          qualityText = '720p';
          description = 'جودة عالية';
          break;
        case VideoQuality.quality1080:
          qualityText = '1080p';
          description = 'جودة عالية جداً';
          break;
        case VideoQuality.auto:
          continue; // تخطي الخيار التلقائي في قائمة التحميل
      }

      items.add(
        PopupMenuItem<String>(
          value: qualityText,
          child: Text('$qualityText - $description'),
        ),
      );
    }

    return items;
  }

  /// بناء زر حذف الفيديو المحمل
  Widget _buildDeleteButton(Video video) {
    return IconButton(
      onPressed: () => _deleteDownloadedVideo(video),
      icon: Icon(Icons.delete_outline, color: Colors.red, size: 24.sp),
      tooltip: 'حذف الفيديو المحمل',
    );
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  /// التعامل مع تشغيل الفيديو - إظهار خيارات التشغيل إذا كان محمل
  Future<void> _handlePlayVideo(Video video, bool isDownloaded) async {
    if (!isDownloaded) {
      // تشغيل عبر الإنترنت مباشرة
      _playVideoOnline(video);
      return;
    }

    try {
      // الحصول على الجودات المحملة
      final downloadedQualities = await _videoService.getDownloadedQualities(
        video.id,
      );

      if (downloadedQualities.isEmpty) {
        // لا توجد جودات محملة - تشغيل عبر الإنترنت
        _playVideoOnline(video);
        return;
      }

      // إظهار خيارات التشغيل
      if (!mounted) return;
      final result = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(
            'خيارات التشغيل',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'اختر طريقة تشغيل "${video.title}":',
                style: TextStyle(fontSize: 14.sp),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 20.h),
              // خيارات التشغيل المحلي
              ...downloadedQualities.map(
                (quality) => Container(
                  width: double.infinity,
                  margin: EdgeInsets.only(bottom: 8.h),
                  child: ElevatedButton(
                    onPressed: () =>
                        Navigator.of(context).pop('local_${quality.name}'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.download_done, size: 20.sp),
                        SizedBox(width: 8.w),
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'تشغيل محلي - ${video.getQualityDisplayText(quality)}',
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              'بدون إنترنت',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.white.withValues(alpha: 0.8),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(height: 8.h),
              // خيار التشغيل عبر الإنترنت
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop('online'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.cloud, size: 20.sp),
                      SizedBox(width: 8.w),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'تشغيل عبر الإنترنت',
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            'يتطلب اتصال إنترنت',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.white.withValues(alpha: 0.8),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'إلغاء',
                style: TextStyle(color: AppTheme.primaryColor, fontSize: 14.sp),
              ),
            ),
          ],
        ),
      );

      if (result != null) {
        if (result == 'online') {
          _playVideoOnline(video);
        } else if (result.startsWith('local_')) {
          final qualityName = result.replaceFirst('local_', '');
          final quality = VideoQuality.values.firstWhere(
            (q) => q.name == qualityName,
            orElse: () => VideoQuality.quality480,
          );
          _playVideoOffline(video, quality);
        }
      }
    } catch (e) {
      debugPrint('خطأ في التعامل مع تشغيل الفيديو: $e');
      // في حالة الخطأ، تشغيل عبر الإنترنت
      _playVideoOnline(video);
    }
  }

  /// تشغيل الفيديو عبر الإنترنت
  void _playVideoOnline(Video video) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            VideoPlayerPage(video: video, isOfflineMode: false),
      ),
    );
  }

  /// تشغيل الفيديو المحمل محلياً
  void _playVideoOffline(Video video, VideoQuality quality) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoPlayerPage(
          video: video,
          preferredQuality: quality,
          isOfflineMode: true,
        ),
      ),
    );
  }

  Future<void> _downloadVideo(Video video, String quality) async {
    // إزالة التحقق من الاشتراك - السماح بالتحميل للجميع
    setState(() {
      _isDownloading[video.id] = true;
      _downloadProgress[video.id] = 0.0;
    });

    try {
      // تحويل الجودة النصية إلى VideoQuality enum
      VideoQuality videoQuality;
      switch (quality) {
        case '360p':
          videoQuality = VideoQuality.quality360;
          break;
        case '480p':
          videoQuality = VideoQuality.quality480;
          break;
        case '720p':
          videoQuality = VideoQuality.quality720;
          break;
        case '1080p':
          videoQuality = VideoQuality.quality1080;
          break;
        default:
          videoQuality = VideoQuality.quality480;
      }

      // استخدام VideoService للتحميل في الخلفية (يستمر حتى عند الخروج من التطبيق)
      final success = await _videoService.downloadVideo(
        video,
        videoQuality,
        onProgress: (progress, received, total) {
          if (mounted) {
            setState(() {
              _downloadProgress[video.id] = progress;
              _downloadedBytes[video.id] = received;
              _totalBytes[video.id] = total;
            });
          }
        },
      );

      if (mounted) {
        setState(() {
          _isDownloading[video.id] = false;
          // تنظيف بيانات التحميل
          _downloadProgress.remove(video.id);
          _downloadedBytes.remove(video.id);
          _totalBytes.remove(video.id);
        });

        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'بدأ تحميل الفيديو في الخلفية - جودة $quality\nسيستمر التحميل حتى عند الخروج من التطبيق',
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في بدء تحميل الفيديو'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isDownloading[video.id] = false;
          // تنظيف بيانات التحميل في حالة الخطأ
          _downloadProgress.remove(video.id);
          _downloadedBytes.remove(video.id);
          _totalBytes.remove(video.id);
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الفيديو: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _cancelDownload(Video video) {
    _videoService.cancelDownload(video.id);
    setState(() {
      _isDownloading[video.id] = false;
      _downloadProgress[video.id] = 0.0;
    });
  }

  void _showDownloadedMessage() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('الفيديو محمل بالفعل')));
  }

  /// حذف فيديو محمل مع إظهار خيارات الجودة
  Future<void> _deleteDownloadedVideo(Video video) async {
    try {
      // الحصول على الجودات المحملة
      final downloadedQualities = await _videoService.getDownloadedQualities(
        video.id,
      );

      if (!mounted) return;

      if (downloadedQualities.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا توجد ملفات محملة لهذا الفيديو'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // إظهار حوار الحذف المحسن
      final result = await _showDeleteVideoDialog(video, downloadedQualities);

      if (!mounted || result == null) return;

      bool success = false;

      if (result == 'all') {
        // حذف جميع الجودات
        success = await _videoService.deleteAllDownloadedQualities(video.id);
        if (mounted && success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف جميع الجودات المحملة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // حذف جودة محددة
        final quality = VideoQuality.values.firstWhere(
          (q) => q.name == result,
          orElse: () => VideoQuality.quality480,
        );
        success = await _videoService.deleteDownloadedVideo(video.id, quality);
        if (mounted && success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم حذف الجودة ${video.getQualityDisplayText(quality)} بنجاح',
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      }

      if (mounted && !success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في حذف الفيديو'),
            backgroundColor: Colors.red,
          ),
        );
      }

      // تحديث الواجهة
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف الفيديو: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_library_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد فيديوهات متاحة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة الفيديوهات قريباً',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// إظهار حوار حذف الفيديو المحسن
  Future<String?> _showDeleteVideoDialog(
    Video video,
    List<VideoQuality> downloadedQualities,
  ) async {
    // إذا كان هناك جودة واحدة فقط، اعرض حوار تأكيد بسيط
    if (downloadedQualities.length == 1) {
      final quality = downloadedQualities.first;
      final qualityText = video.getQualityDisplayText(quality);

      return await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('حذف الفيديو المحمل'),
          content: Text('حذف الفيديو المحمل بالجودة $qualityText'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(quality.name),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('موافق'),
            ),
          ],
        ),
      );
    }

    // إذا كان هناك أكثر من جودة، اعرض قائمة الخيارات
    return await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الفيديو المحمل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('اختر الجودة المراد حذفها من "${video.title}":'),
            const SizedBox(height: 16),
            ...downloadedQualities.map(
              (quality) => Container(
                margin: const EdgeInsets.symmetric(vertical: 4),
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(quality.name),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red.shade50,
                    foregroundColor: Colors.red,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                  child: Text(
                    'حذف جودة ${video.getQualityDisplayText(quality)}',
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop('all'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 48),
              ),
              child: const Text('حذف جميع الجودات'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }
}
