import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_section_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/services/subscription_service.dart';
import '../../../../shared/services/simple_data_service.dart';
import 'video_subjects_page.dart';
import 'free_video_sections_page.dart';

/// صفحة أقسام الفيديوهات للطالب - مُعاد كتابتها بالكامل
class VideoSectionsPage extends StatefulWidget {
  const VideoSectionsPage({super.key});

  @override
  State<VideoSectionsPage> createState() => _VideoSectionsPageState();
}

class _VideoSectionsPageState extends State<VideoSectionsPage> {
  final VideoService _videoService = VideoService.instance;
  final SubscriptionService _subscriptionService = SubscriptionService.instance;

  List<VideoSection> _sections = [];
  bool _isNetworkLoading = false; // فقط للتحميل من الشبكة
  bool _hasSubscription = false;
  bool _isRefreshing = false;
  bool _hasTriedLoading = false; // لتتبع ما إذا حاولنا التحميل

  @override
  void initState() {
    super.initState();
    _loadDataImmediately();
  }

  /// تحميل البيانات فوراً بدون انتظار
  void _loadDataImmediately() {
    // محاولة تحميل البيانات المحفوظة أولاً
    _loadSavedDataFirst();
  }

  /// تحميل البيانات المحفوظة أولاً
  Future<void> _loadSavedDataFirst() async {
    try {
      // محاولة تحميل البيانات المحفوظة من Firebase
      final savedSections = await _videoService.getPaidVideoSections();

      if (savedSections.isNotEmpty) {
        setState(() {
          _sections = savedSections;
          _hasTriedLoading = true;
        });
        debugPrint(
          '🚀 تم عرض ${savedSections.length} قسم فيديو من البيانات المحفوظة',
        );
      } else {
        // إذا لم توجد بيانات محفوظة، استخدم البيانات الافتراضية
        final defaultSections = SimpleDataService.instance.getVideoSections();
        setState(() {
          _sections = defaultSections;
          _hasTriedLoading = true;
        });
        debugPrint(
          '🚀 تم عرض ${defaultSections.length} قسم فيديو من البيانات الافتراضية',
        );
      }
    } catch (e) {
      // في حالة الخطأ، استخدم البيانات الافتراضية
      final defaultSections = SimpleDataService.instance.getVideoSections();
      setState(() {
        _sections = defaultSections;
        _hasTriedLoading = true;
      });
      debugPrint(
        '❌ خطأ في تحميل البيانات المحفوظة، استخدام البيانات الافتراضية: $e',
      );
    }

    // تحميل بيانات الاشتراك في الخلفية
    _loadSubscriptionInBackground();
  }

  /// تحميل بيانات الاشتراك في الخلفية
  Future<void> _loadSubscriptionInBackground() async {
    try {
      await _subscriptionService.loadUserSubscription();
      final hasSubscription = _subscriptionService.hasActiveSubscription();

      if (mounted) {
        setState(() {
          _hasSubscription = hasSubscription;
        });
      }

      // تحديث صامت من الشبكة في الخلفية
      _updateFromNetworkSilently();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات الاشتراك: $e');
    }
  }

  /// تحديث صامت من الشبكة في الخلفية
  Future<void> _updateFromNetworkSilently() async {
    try {
      final hasInternet = await _checkInternetConnection();
      if (!hasInternet) {
        debugPrint('📱 لا توجد شبكة - الاعتماد على البيانات المحلية فقط');
        return;
      }

      debugPrint('🌐 تحديث أقسام الفيديوهات من الشبكة في الخلفية...');
      final updatedSections = await _videoService
          .refreshVideoSectionsFromFirebase();

      if (mounted && updatedSections.isNotEmpty) {
        setState(() {
          _sections = updatedSections;
        });
        debugPrint('🔄 تم تحديث ${updatedSections.length} قسم فيديو من الشبكة');
      }
    } catch (e) {
      debugPrint(
        '⚠️ فشل التحديث من الشبكة (لا مشكلة - البيانات المحلية متوفرة): $e',
      );
    }
  }

  /// فحص الاتصال بالإنترنت
  Future<bool> _checkInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// تحديث يدوي (Pull-to-Refresh)
  Future<void> _refreshData() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      debugPrint('🔄 تحديث يدوي لأقسام الفيديوهات...');
      final sections = await _videoService.refreshVideoSectionsFromFirebase();
      final hasSubscription = _subscriptionService.hasActiveSubscription();

      if (mounted) {
        setState(() {
          _sections = sections;
          _hasSubscription = hasSubscription;
          _isRefreshing = false;
        });
        debugPrint('✅ تم تحديث ${sections.length} قسم فيديو يدوياً');
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحديث اليدوي: $e');
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في التحديث: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'الفيديوهات',
        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: Container(
        decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
      ),
      foregroundColor: Colors.white,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _isRefreshing ? null : _refreshData,
          tooltip: 'تحديث',
        ),
        IconButton(
          icon: const Icon(Icons.play_circle_outline),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const FreeVideoSectionsPage(),
              ),
            );
          },
          tooltip: 'تجربة التطبيق',
        ),
      ],
    );
  }

  Widget _buildBody() {
    // إذا كان هناك بيانات، نعرضها فوراً
    if (_sections.isNotEmpty) {
      return _buildSectionsContent();
    }

    // إذا كان يحمل من الشبكة، نعرض مؤشر التحميل
    if (_isNetworkLoading) {
      return _buildLoadingIndicator();
    }

    // إذا لم نحاول التحميل بعد، نعرض مؤشر التحميل
    if (!_hasTriedLoading) {
      return _buildLoadingIndicator();
    }

    // فقط إذا حاولنا التحميل ولم توجد بيانات، نعرض رسالة فارغة
    return _buildEmptyState();
  }

  Widget _buildLoadingIndicator() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildSectionsContent() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: 20.h),
            Expanded(child: _buildSectionsList()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(Icons.video_library, color: Colors.white, size: 24.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'أقسام الفيديوهات',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  '${_sections.length} قسم متاح',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionsList() {
    return ListView.builder(
      itemCount: _sections.length,
      itemBuilder: (context, index) {
        final section = _sections[index];
        return _buildSectionCard(section);
      },
    );
  }

  Widget _buildSectionCard(VideoSection section) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16.r),
          onTap: () => _navigateToSubjects(section),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                _buildSectionIcon(section),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        section.name,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      if (section.description.isNotEmpty) ...[
                        SizedBox(height: 4.h),
                        Text(
                          section.description,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppTheme.textSecondaryColor,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppTheme.textSecondaryColor,
                  size: 16.sp,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionIcon(VideoSection section) {
    return Container(
      width: 50.w,
      height: 50.w,
      decoration: BoxDecoration(
        gradient: _getSectionGradient(section.color),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Icon(Icons.video_collection, color: Colors.white, size: 24.sp),
    );
  }

  LinearGradient _getSectionGradient(String color) {
    // يمكن تخصيص الألوان حسب اللون المحفوظ
    return AppTheme.primaryGradient;
  }

  void _navigateToSubjects(VideoSection section) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoSubjectsPage(
          sectionId: section.id,
          hasSubscription: _hasSubscription,
          isFreeSection: section.isFree,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_library_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد أقسام فيديوهات متاحة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة الفيديوهات قريباً',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
