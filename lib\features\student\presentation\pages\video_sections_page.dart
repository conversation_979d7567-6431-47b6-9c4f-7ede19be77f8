import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/adaptive_sizing.dart';
import '../../../../core/widgets/adaptive_widgets.dart';
import '../../../../shared/models/video_section_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/services/subscription_service.dart';

import 'video_subjects_page.dart';
import 'free_video_sections_page.dart';

/// صفحة أقسام الفيديوهات للطالب - مُعاد كتابتها بالكامل
class VideoSectionsPage extends StatefulWidget {
  const VideoSectionsPage({super.key});

  @override
  State<VideoSectionsPage> createState() => _VideoSectionsPageState();
}

class _VideoSectionsPageState extends State<VideoSectionsPage> {
  final VideoService _videoService = VideoService.instance;
  final SubscriptionService _subscriptionService = SubscriptionService.instance;

  List<VideoSection> _sections = [];
  bool _isLoading = true; // تحميل البيانات المحفوظة
  bool _isNetworkLoading = false; // تحميل من الشبكة
  bool _hasSubscription = false;
  bool _isRefreshing = false;
  bool _hasTriedLoading = false; // هل تم محاولة التحميل

  @override
  void initState() {
    super.initState();
    _loadDataImmediately();
  }

  /// تحميل البيانات فوراً بدون انتظار
  void _loadDataImmediately() {
    // محاولة تحميل البيانات المحفوظة أولاً
    _loadSavedDataFirst();
  }

  /// تحميل البيانات المحفوظة أولاً
  Future<void> _loadSavedDataFirst() async {
    try {
      // محاولة تحميل البيانات المحفوظة من Firebase
      final savedSections = await _videoService.getPaidVideoSections();

      if (savedSections.isNotEmpty) {
        setState(() {
          _sections = savedSections;
          _isLoading = false;
          _hasTriedLoading = true;
        });
        debugPrint(
          '🚀 تم عرض ${savedSections.length} قسم فيديو من البيانات المحفوظة',
        );
      } else {
        setState(() {
          _isLoading = false;
          _hasTriedLoading = true;
        });
        debugPrint('📱 لا توجد أقسام فيديو محفوظة - سيتم التحميل من الشبكة');
      }

      // تحديث البيانات في الخلفية بصمت (سواء كانت موجودة أم لا)
      _updateFromNetworkSilently();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل أقسام الفيديو: $e');
      setState(() {
        _isLoading = false;
        _hasTriedLoading = true;
      });
    }

    // تحميل بيانات الاشتراك في الخلفية
    _loadSubscriptionInBackground();
  }

  /// تحميل بيانات الاشتراك في الخلفية
  Future<void> _loadSubscriptionInBackground() async {
    try {
      await _subscriptionService.loadUserSubscription();
      final hasSubscription = _subscriptionService.hasActiveSubscription();

      if (mounted) {
        setState(() {
          _hasSubscription = hasSubscription;
        });
      }

      // تحديث صامت من الشبكة في الخلفية
      _updateFromNetworkSilently();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات الاشتراك: $e');
    }
  }

  /// تحديث صامت من الشبكة في الخلفية
  Future<void> _updateFromNetworkSilently() async {
    if (_isNetworkLoading) return; // تجنب التحديث المتعدد

    try {
      setState(() {
        _isNetworkLoading = true;
      });

      debugPrint('🔄 تحديث أقسام الفيديوهات في الخلفية...');

      // محاولة التحديث من Firebase
      await _videoService.refreshVideoSectionsFromFirebase();
      final updatedSections = await _videoService.getPaidVideoSections();

      if (mounted) {
        if (updatedSections.isNotEmpty) {
          setState(() {
            _sections = updatedSections;
            _isNetworkLoading = false;
          });
          debugPrint(
            '✅ تم تحديث ${updatedSections.length} قسم فيديو من Firebase في الخلفية',
          );
        } else {
          setState(() {
            _isNetworkLoading = false;
          });
          debugPrint('⚠️ لم يتم العثور على أقسام فيديو من Firebase');
        }
      }
    } catch (e) {
      setState(() {
        _isNetworkLoading = false;
      });
      debugPrint('❌ خطأ في التحديث في الخلفية: $e');
    }
  }

  /// تحديث يدوي (Pull-to-Refresh)
  Future<void> _refreshData() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      debugPrint('🔄 تحديث يدوي لأقسام الفيديوهات...');
      // تحديث جميع الأقسام من Firebase أولاً
      await _videoService.refreshVideoSectionsFromFirebase();

      // ثم جلب الأقسام المدفوعة فقط
      final sections = await _videoService.getPaidVideoSections();
      final hasSubscription = _subscriptionService.hasActiveSubscription();

      if (mounted) {
        setState(() {
          _sections = sections;
          _hasSubscription = hasSubscription;
          _isRefreshing = false;
        });
        debugPrint('✅ تم تحديث ${sections.length} قسم فيديو مدفوع يدوياً');
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحديث اليدوي: $e');
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في التحديث: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AdaptiveAppBar(
      title: 'الفيديوهات',
      gradient: AppTheme.primaryGradient,
      actions: [
        if (_isNetworkLoading)
          Padding(
            padding: EdgeInsets.only(left: 8.adaptiveSpacing),
            child: SizedBox(
              width: 16.adaptiveIcon,
              height: 16.adaptiveIcon,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
          ),
        // زر تجربة التطبيق مع شعار الفيديو
        Container(
          margin: EdgeInsets.only(left: 8.adaptiveSpacing),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20.adaptiveRadius),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const FreeVideoSectionsPage(),
                  ),
                );
              },
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.adaptiveSpacing,
                  vertical: 6.adaptiveSpacing,
                ),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20.adaptiveRadius),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AdaptiveIcon(
                      Icons.play_circle_outline,
                      size: 20,
                      color: Colors.white,
                    ),
                    SizedBox(width: 4.adaptiveSpacing),
                    AdaptiveText(
                      'تجربة التطبيق',
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    // إذا كان هناك بيانات، نعرضها فوراً
    if (_sections.isNotEmpty) {
      return RefreshIndicator(
        onRefresh: _refreshData,
        child: _buildSectionsContent(),
      );
    }

    // إذا كان يحمل من الشبكة، نعرض مؤشر التحميل
    if (_isNetworkLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل أقسام الفيديوهات...'),
          ],
        ),
      );
    }

    // إذا كان التحميل الأولي، نعرض مؤشر التحميل
    if (_isLoading || !_hasTriedLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    // فقط إذا حاولنا التحميل ولم توجد بيانات، نعرض رسالة فارغة
    return _buildEmptyState();
  }

  Widget _buildSectionsContent() {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return isLandscape
        ? SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCompactHeader(),
                SizedBox(height: 16.h),
                _buildCompactSectionsGrid(),
              ],
            ),
          )
        : Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                SizedBox(height: 20.h),
                Expanded(child: _buildSectionsGrid()),
              ],
            ),
          );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(Icons.video_library, color: Colors.white, size: 24.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'أقسام الفيديوهات',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'اختر القسم لمشاهدة الفيديوهات التعليمية',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(Icons.video_library, color: Colors.white, size: 20.sp),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              'أقسام الفيديوهات - اختر القسم لمشاهدة الفيديوهات التعليمية',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionsGrid() {
    return AdaptiveGridView(
      shrinkWrap: false,
      children: _sections.map((section) => _buildSectionCard(section)).toList(),
    );
  }

  Widget _buildCompactSectionsGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16.w,
        mainAxisSpacing: 16.h,
        childAspectRatio: 1.1,
      ),
      itemCount: _sections.length,
      itemBuilder: (context, index) {
        final section = _sections[index];
        return _buildSectionCard(section);
      },
    );
  }

  Widget _buildSectionCard(VideoSection section) {
    return AdaptiveSectionCard(
      title: section.name,
      subtitle: section.description,
      backgroundColor: _getSectionColor(section.color),
      onTap: () => _navigateToSubjects(section),
      leading: AdaptiveIcon(
        Icons.video_collection,
        size: 32,
        color: Colors.white,
      ),
    );
  }

  Color _getSectionColor(String colorString) {
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return AppTheme.primaryColor; // لون افتراضي في حالة الخطأ
    }
  }

  void _navigateToSubjects(VideoSection section) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoSubjectsPage(
          sectionId: section.id,
          hasSubscription: _hasSubscription,
          isFreeSection: section.isFree,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AdaptiveIcon(
            Icons.video_library_outlined,
            size: 80,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.adaptiveSpacing),
          AdaptiveText(
            'لا توجد أقسام فيديوهات متاحة',
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
          SizedBox(height: 8.adaptiveSpacing),
          AdaptiveText(
            'تأكد من الاتصال بالإنترنت وحاول مرة أخرى',
            fontSize: 14,
            color: AppTheme.textSecondaryColor,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.adaptiveSpacing),
          AdaptiveElevatedButton(
            onPressed: _isNetworkLoading ? null : _updateFromNetworkSilently,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                AdaptiveIcon(Icons.refresh, size: 20, color: Colors.white),
                SizedBox(width: 8.adaptiveSpacing),
                AdaptiveText(
                  'إعادة المحاولة',
                  fontSize: 14,
                  color: Colors.white,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
