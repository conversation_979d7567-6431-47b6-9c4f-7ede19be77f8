import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_subject_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/services/subscription_service.dart';
import '../../../../shared/services/simple_data_service.dart';
import 'video_units_page.dart';

/// صفحة مواد الفيديوهات للطالب - مُعاد كتابتها بالكامل
class VideoSubjectsPage extends StatefulWidget {
  final String sectionId;
  final bool hasSubscription;
  final bool isFreeSection;

  const VideoSubjectsPage({
    super.key,
    required this.sectionId,
    required this.hasSubscription,
    required this.isFreeSection,
  });

  @override
  State<VideoSubjectsPage> createState() => _VideoSubjectsPageState();
}

class _VideoSubjectsPageState extends State<VideoSubjectsPage> {
  final VideoService _videoService = VideoService.instance;
  final SubscriptionService _subscriptionService = SubscriptionService.instance;

  List<VideoSubject> _subjects = [];
  bool _isNetworkLoading = false;
  bool _isRefreshing = false;
  bool _hasTriedLoading = false;

  @override
  void initState() {
    super.initState();
    _loadDataImmediately();
  }

  /// تحميل البيانات فوراً بدون انتظار
  void _loadDataImmediately() {
    // محاولة تحميل البيانات المحفوظة أولاً
    _loadSavedDataFirst();
  }

  /// تحميل البيانات المحفوظة أولاً
  Future<void> _loadSavedDataFirst() async {
    try {
      // فحص البيانات المحلية من الكاش أولاً (بدون await)
      final allCachedSubjects = _videoService.cachedVideoSubjects;
      final cachedSectionSubjects = allCachedSubjects
          .where(
            (subject) =>
                subject.sectionId == widget.sectionId && subject.isActive,
          )
          .toList();

      if (cachedSectionSubjects.isNotEmpty) {
        // البيانات متوفرة في الكاش - عرضها فوراً
        setState(() {
          _subjects = cachedSectionSubjects;
          _hasTriedLoading = true;
        });
        debugPrint(
          '⚡ إرجاع ${cachedSectionSubjects.length} مادة فيديو من الكاش فوراً',
        );
        debugPrint(
          '🚀 تم عرض ${cachedSectionSubjects.length} مادة فيديو من البيانات المحفوظة',
        );

        // تحميل بيانات الاشتراك في الخلفية
        _loadSubscriptionInBackground();

        // بدء التحديث في الخلفية
        _updateFromNetworkSilently();
        return;
      }

      // إذا لم توجد بيانات في الكاش، استخدم البيانات الافتراضية
      final allDefaultSubjects = SimpleDataService.instance.getVideoSubjects();
      final defaultSectionSubjects = allDefaultSubjects
          .where(
            (subject) =>
                subject.sectionId == widget.sectionId && subject.isActive,
          )
          .toList();

      setState(() {
        _subjects = defaultSectionSubjects;
        _hasTriedLoading = true;
      });
      debugPrint(
        '🚀 تم عرض ${defaultSectionSubjects.length} مادة فيديو من البيانات الافتراضية',
      );

      // تحميل بيانات الاشتراك في الخلفية
      _loadSubscriptionInBackground();

      // محاولة تحميل البيانات من Firebase
      try {
        final allSavedSubjects = await _videoService.getAllVideoSubjects();
        final savedSectionSubjects = allSavedSubjects
            .where(
              (subject) =>
                  subject.sectionId == widget.sectionId && subject.isActive,
            )
            .toList();

        if (savedSectionSubjects.isNotEmpty && mounted) {
          setState(() {
            _subjects = savedSectionSubjects;
          });
          debugPrint(
            '✅ تم تحديث ${savedSectionSubjects.length} مادة فيديو من Firebase',
          );
        }
      } catch (e) {
        debugPrint('❌ خطأ في تحميل البيانات من Firebase: $e');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات: $e');
      // في حالة الخطأ، استخدم البيانات الافتراضية
      final allDefaultSubjects = SimpleDataService.instance.getVideoSubjects();
      final defaultSectionSubjects = allDefaultSubjects
          .where(
            (subject) =>
                subject.sectionId == widget.sectionId && subject.isActive,
          )
          .toList();
      setState(() {
        _subjects = defaultSectionSubjects;
        _hasTriedLoading = true;
      });
      debugPrint(
        '🚀 تم عرض ${defaultSectionSubjects.length} مادة فيديو من البيانات الافتراضية',
      );

      // تحميل بيانات الاشتراك في الخلفية
      _loadSubscriptionInBackground();
    }
  }

  /// تحميل بيانات الاشتراك في الخلفية
  Future<void> _loadSubscriptionInBackground() async {
    try {
      await _subscriptionService.loadUserSubscription();
      // تحديث صامت من الشبكة في الخلفية
      _updateFromNetworkSilently();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات الاشتراك: $e');
    }
  }

  /// تحديث صامت من الشبكة في الخلفية
  Future<void> _updateFromNetworkSilently() async {
    try {
      final hasInternet = await _checkInternetConnection();
      if (!hasInternet) {
        debugPrint('📱 لا توجد شبكة - الاعتماد على البيانات المحلية فقط');
        return;
      }

      debugPrint('🌐 تحديث مواد الفيديوهات من الشبكة في الخلفية...');
      final updatedSubjects = await _videoService
          .refreshVideoSubjectsFromFirebase(widget.sectionId);

      if (mounted && updatedSubjects.isNotEmpty) {
        setState(() {
          _subjects = updatedSubjects;
        });
        debugPrint(
          '🔄 تم تحديث ${updatedSubjects.length} مادة فيديو من الشبكة',
        );
      }
    } catch (e) {
      debugPrint(
        '⚠️ فشل التحديث من الشبكة (لا مشكلة - البيانات المحلية متوفرة): $e',
      );
    }
  }

  /// فحص الاتصال بالإنترنت
  Future<bool> _checkInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// تحديث يدوي (Pull-to-Refresh)
  Future<void> _refreshData() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      debugPrint('🔄 تحديث يدوي لمواد الفيديوهات...');
      final subjects = await _videoService.refreshVideoSubjectsFromFirebase(
        widget.sectionId,
      );

      if (mounted) {
        setState(() {
          _subjects = subjects;
          _isRefreshing = false;
        });
        debugPrint('✅ تم تحديث ${subjects.length} مادة فيديو يدوياً');
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحديث اليدوي: $e');
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في التحديث: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'المواد',
        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: Container(
        decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
      ),
      foregroundColor: Colors.white,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _isRefreshing ? null : _refreshData,
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  Widget _buildBody() {
    // إذا كان هناك بيانات، نعرضها فوراً
    if (_subjects.isNotEmpty) {
      return _buildSubjectsContent();
    }

    // إذا كان يحمل من الشبكة، نعرض مؤشر التحميل
    if (_isNetworkLoading) {
      return _buildLoadingIndicator();
    }

    // إذا لم نحاول التحميل بعد، نعرض مؤشر التحميل
    if (!_hasTriedLoading) {
      return _buildLoadingIndicator();
    }

    // فقط إذا حاولنا التحميل ولم توجد بيانات، نعرض رسالة فارغة
    return _buildEmptyState();
  }

  Widget _buildLoadingIndicator() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildSubjectsContent() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: 20.h),
            Expanded(child: _buildSubjectsList()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(Icons.subject, color: Colors.white, size: 24.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مواد الفيديوهات',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  '${_subjects.length} مادة متاحة',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubjectsList() {
    return ListView.builder(
      itemCount: _subjects.length,
      itemBuilder: (context, index) {
        final subject = _subjects[index];
        return _buildSubjectCard(subject);
      },
    );
  }

  Widget _buildSubjectCard(VideoSubject subject) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16.r),
          onTap: () => _navigateToUnits(subject),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                _buildSubjectIcon(subject),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        subject.name,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      if (subject.description.isNotEmpty) ...[
                        SizedBox(height: 4.h),
                        Text(
                          subject.description,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppTheme.textSecondaryColor,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppTheme.textSecondaryColor,
                  size: 16.sp,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSubjectIcon(VideoSubject subject) {
    return Container(
      width: 50.w,
      height: 50.w,
      decoration: BoxDecoration(
        gradient: _getSubjectGradient(subject.color),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Icon(Icons.video_collection, color: Colors.white, size: 24.sp),
    );
  }

  LinearGradient _getSubjectGradient(String color) {
    return AppTheme.primaryGradient;
  }

  void _navigateToUnits(VideoSubject subject) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoUnitsPage(
          subjectId: subject.id,
          hasSubscription: widget.hasSubscription,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_collection_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد مواد فيديوهات متاحة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة المواد قريباً',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
