import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';

/// خدمة التحميل في الخلفية
/// تدير تحميل الفيديوهات حتى عند الخروج من التطبيق
class BackgroundDownloadService {
  static const MethodChannel _channel = MethodChannel(
    'com.smarttest.background_download',
  );

  static BackgroundDownloadService? _instance;
  static BackgroundDownloadService get instance {
    _instance ??= BackgroundDownloadService._();
    return _instance!;
  }

  BackgroundDownloadService._() {
    _setupMethodCallHandler();
  }

  // Map لتتبع حالة التحميل
  final Map<String, bool> _downloadingVideos = {};
  final Map<String, int> _downloadProgress = {};

  // Callbacks للتحديثات
  Function(String videoId, int progress, int received, int total)?
  onProgressUpdate;
  Function(String videoId)? onDownloadComplete;
  Function(String videoId, String error)? onDownloadFailed;
  Function(String videoId)? onDownloadCancelled;

  /// إعداد معالج استدعاءات الطرق من Android
  void _setupMethodCallHandler() {
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'downloadVideoInBackground':
          await _handleBackgroundDownload(call.arguments);
          break;
        case 'downloadProgress':
          _handleProgressUpdate(call.arguments);
          break;
        case 'downloadCompleted':
          _handleDownloadComplete(call.arguments);
          break;
        case 'downloadFailed':
          _handleDownloadFailed(call.arguments);
          break;
        case 'downloadCancelled':
          _handleDownloadCancelled(call.arguments);
          break;
      }
    });
  }

  /// بدء تحميل فيديو في الخلفية
  Future<bool> startBackgroundDownload({
    required String videoId,
    required String videoTitle,
    required String videoUrl,
    required String quality,
    required String filePath,
  }) async {
    try {
      debugPrint('🚀 بدء تحميل فيديو في الخلفية: $videoTitle - $quality');

      _downloadingVideos[videoId] = true;
      _downloadProgress[videoId] = 0;

      final result = await _channel.invokeMethod('startBackgroundDownload', {
        'videoId': videoId,
        'videoTitle': videoTitle,
        'videoUrl': videoUrl,
        'quality': quality,
        'filePath': filePath,
      });

      return result == true;
    } catch (e) {
      debugPrint('❌ خطأ في بدء التحميل في الخلفية: $e');
      _downloadingVideos.remove(videoId);
      _downloadProgress.remove(videoId);
      return false;
    }
  }

  /// إلغاء تحميل فيديو محدد
  Future<bool> cancelBackgroundDownload(String videoId) async {
    try {
      debugPrint('🚫 إلغاء تحميل فيديو: $videoId');

      final result = await _channel.invokeMethod('cancelBackgroundDownload', {
        'videoId': videoId,
      });

      _downloadingVideos.remove(videoId);
      _downloadProgress.remove(videoId);

      return result == true;
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء التحميل: $e');
      return false;
    }
  }

  /// إلغاء جميع التحميلات
  Future<bool> cancelAllBackgroundDownloads() async {
    try {
      debugPrint('🚫 إلغاء جميع التحميلات');

      final result = await _channel.invokeMethod(
        'cancelAllBackgroundDownloads',
      );

      _downloadingVideos.clear();
      _downloadProgress.clear();

      return result == true;
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء جميع التحميلات: $e');
      return false;
    }
  }

  /// التحقق من حالة التحميل
  Future<bool> isDownloading(String videoId) async {
    try {
      final result = await _channel.invokeMethod('isDownloading', {
        'videoId': videoId,
      });
      return result == true;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من حالة التحميل: $e');
      return _downloadingVideos[videoId] ?? false;
    }
  }

  /// الحصول على تقدم التحميل
  Future<int> getDownloadProgress(String videoId) async {
    try {
      final result = await _channel.invokeMethod('getDownloadProgress', {
        'videoId': videoId,
      });
      return result ?? 0;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على تقدم التحميل: $e');
      return _downloadProgress[videoId] ?? 0;
    }
  }

  /// تحديث تقدم التحميل من Flutter إلى Android
  Future<void> updateDownloadProgress(String videoId, int progress) async {
    try {
      _downloadProgress[videoId] = progress;
      await _channel.invokeMethod('updateDownloadProgress', {
        'videoId': videoId,
        'progress': progress,
      });
    } catch (e) {
      debugPrint('❌ خطأ في تحديث تقدم التحميل: $e');
    }
  }

  /// معالجة طلب التحميل من Android
  Future<void> _handleBackgroundDownload(Map<String, dynamic> arguments) async {
    final videoId = arguments['videoId'] as String;
    final videoTitle = arguments['videoTitle'] as String;
    final videoUrl = arguments['videoUrl'] as String;
    final quality = arguments['quality'] as String;
    final filePath = arguments['filePath'] as String;

    debugPrint('📱 بدء تحميل فيديو: $videoTitle ($quality)');

    try {
      // استخدام Dio للتحميل مع تتبع التقدم
      final dio = Dio();

      await dio.download(
        videoUrl,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = ((received / total) * 100).round();

            // تحديث التقدم في Android
            updateDownloadProgress(videoId, progress);

            // إرسال تحديث التقدم للـ UI
            _handleProgressUpdate({
              'videoId': videoId,
              'progress': progress,
              'received': received,
              'total': total,
            });

            debugPrint('📊 تقدم التحميل: $progress% ($received/$total bytes)');
          }
        },
      );

      debugPrint('✅ اكتمل تحميل الفيديو: $videoTitle');

      // إشعار Android بالاكتمال
      await _channel.invokeMethod('notifyDownloadComplete', {
        'videoId': videoId,
      });

      // إشعار Flutter بالاكتمال
      _handleDownloadComplete({'videoId': videoId});
    } catch (e) {
      debugPrint('❌ فشل تحميل الفيديو: $e');

      // إشعار Android بالفشل
      await _channel.invokeMethod('notifyDownloadFailed', {'videoId': videoId});

      // إشعار Flutter بالفشل
      _handleDownloadFailed({'videoId': videoId, 'error': e.toString()});
    }
  }

  /// معالجة تحديث التقدم
  void _handleProgressUpdate(Map<String, dynamic> arguments) {
    final videoId = arguments['videoId'] as String;
    final progress = arguments['progress'] as int;
    final received = arguments['received'] as int? ?? 0;
    final total = arguments['total'] as int? ?? 0;

    _downloadProgress[videoId] = progress;
    onProgressUpdate?.call(videoId, progress, received, total);
  }

  /// معالجة اكتمال التحميل
  void _handleDownloadComplete(Map<String, dynamic> arguments) {
    final videoId = arguments['videoId'] as String;

    _downloadingVideos.remove(videoId);
    _downloadProgress.remove(videoId);
    onDownloadComplete?.call(videoId);
  }

  /// معالجة فشل التحميل
  void _handleDownloadFailed(Map<String, dynamic> arguments) {
    final videoId = arguments['videoId'] as String;
    final error = arguments['error'] as String? ?? 'خطأ غير معروف';

    _downloadingVideos.remove(videoId);
    _downloadProgress.remove(videoId);
    onDownloadFailed?.call(videoId, error);
  }

  /// معالجة إلغاء التحميل
  void _handleDownloadCancelled(Map<String, dynamic> arguments) {
    final videoId = arguments['videoId'] as String;

    _downloadingVideos.remove(videoId);
    _downloadProgress.remove(videoId);
    onDownloadCancelled?.call(videoId);
  }

  /// الحصول على قائمة الفيديوهات قيد التحميل
  List<String> get downloadingVideoIds => _downloadingVideos.keys.toList();

  /// الحصول على تقدم التحميل المحلي
  int getLocalProgress(String videoId) => _downloadProgress[videoId] ?? 0;

  /// التحقق من حالة التحميل المحلية
  bool isLocallyDownloading(String videoId) =>
      _downloadingVideos[videoId] ?? false;

  /// تنظيف البيانات
  void dispose() {
    _downloadingVideos.clear();
    _downloadProgress.clear();
    onProgressUpdate = null;
    onDownloadComplete = null;
    onDownloadFailed = null;
    onDownloadCancelled = null;
  }
}
