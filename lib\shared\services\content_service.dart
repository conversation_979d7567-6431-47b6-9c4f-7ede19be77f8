import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/unit_model.dart';
import '../models/subject_model.dart';
import '../models/lesson_model.dart';
import 'offline_storage_service.dart';
import 'simple_data_service.dart';
import 'persistent_storage_service.dart';

class ContentService extends ChangeNotifier {
  static final ContentService _instance = ContentService._internal();
  static ContentService get instance => _instance;

  // تخزين مؤقت للمواد (كاش في الذاكرة مثل SectionService)
  List<Subject> _cachedSubjects = [];
  List<Subject> _subjects = []; // للتوافق مع الكود القديم
  bool _isInitialized = false; // للتأكد من انتهاء التحميل الأولي
  DateTime? _lastSubjectsUpdate;

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final PersistentStorageService _persistentStorage =
      PersistentStorageService.instance;

  ContentService._internal() {
    // سيتم تحميل البيانات في initialize() مثل VideoService
  }

  /// تهيئة الخدمة وتحميل البيانات المحفوظة (مثل VideoService.initialize)
  Future<void> initialize() async {
    if (_isInitialized) return; // تجنب التحميل المتكرر

    try {
      debugPrint('📱 تحميل المواد المحفوظة دائمياً...');

      // تحميل البيانات المحفوظة دائمياً (مثل VideoService)
      _cachedSubjects = await _persistentStorage.loadSubjects();

      if (_cachedSubjects.isNotEmpty) {
        debugPrint(
          '📱 تم تحميل ${_cachedSubjects.length} مادة من التخزين الدائم',
        );
      } else {
        // إذا لم توجد بيانات محفوظة، حاول تحميل من التخزين المحلي القديم
        final offlineSubjects = await OfflineStorageService.instance
            .getOfflineSubjects();
        if (offlineSubjects.isNotEmpty) {
          _cachedSubjects = offlineSubjects;
          // حفظ في التخزين الدائم للمرات القادمة
          await _persistentStorage.saveSubjects(_cachedSubjects);
          debugPrint(
            '📱 تم تحميل ${_cachedSubjects.length} مادة من التخزين المحلي القديم',
          );
        } else {
          debugPrint('📱 لا توجد مواد محفوظة في التخزين الدائم أو المحلي');
        }
      }

      // تحديث _subjects للتوافق مع الكود القديم
      _subjects = _cachedSubjects;

      debugPrint('✅ تم تحميل البيانات الدائمة: ${_cachedSubjects.length} مادة');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات المحفوظة: $e');
      _cachedSubjects = [];
      _subjects = [];
    } finally {
      _isInitialized = true; // تم انتهاء التحميل الأولي
    }
  }

  /// الحصول على المواد المحفوظة مباشرة من الكاش (مثل SectionService.paidSections)
  List<Subject> get cachedSubjects => _subjects;

  /// الحصول على المواد المدفوعة لقسم معين - قراءة فورية من الذاكرة
  List<Subject> getPaidSubjects({String? sectionId}) {
    var paidSubjects = _cachedSubjects.where((s) => s.isActive);

    // تصفية حسب القسم إذا تم تحديده
    if (sectionId != null) {
      paidSubjects = paidSubjects.where((s) => s.sectionId == sectionId);
    }

    final result = paidSubjects.toList();
    debugPrint(
      '⚡ إرجاع ${result.length} مادة فوراً من الذاكرة${sectionId != null ? ' للقسم $sectionId' : ''}',
    );
    return result;
  }

  /// تحميل المواد من التخزين الدائم
  Future<List<Subject>> loadSubjectsFromStorage({String? sectionId}) async {
    try {
      // محاولة تحميل المواد من التخزين الدائم
      final persistentSubjects = await _persistentStorage.loadSubjects();
      if (persistentSubjects.isNotEmpty) {
        _cachedSubjects = persistentSubjects;
        _subjects = persistentSubjects; // تحديث الكاش القديم أيضاً

        var paidSubjects = persistentSubjects.where((s) => s.isActive);

        // تصفية حسب القسم إذا تم تحديده
        if (sectionId != null) {
          paidSubjects = paidSubjects.where((s) => s.sectionId == sectionId);
        }

        final result = paidSubjects.toList();
        debugPrint(
          '⚡ تم تحميل ${result.length} مادة مدفوعة من التخزين الدائم${sectionId != null ? ' للقسم $sectionId' : ''}',
        );
        return result;
      }

      // إذا لم توجد بيانات، أرجع قائمة فارغة
      debugPrint(
        '📱 لا توجد مواد محفوظة${sectionId != null ? ' للقسم $sectionId' : ''}',
      );
      return [];
    } catch (e) {
      debugPrint('❌ خطأ في تحميل المواد من التخزين: $e');
      return [];
    }
  }

  /// الحصول على المواد فقط (بدون تحديث تلقائي) - مثل getPaidSections
  Future<List<Subject>> getCachedSubjects() async {
    try {
      // انتظار انتهاء التحميل الأولي (مثل SectionService)
      while (!_isInitialized) {
        await Future.delayed(const Duration(milliseconds: 50));
      }

      // إذا كانت المواد محفوظة في الكاش، أرجعها فوراً (مثل SectionService)
      if (_cachedSubjects.isNotEmpty) {
        return _cachedSubjects;
      }

      // محاولة تحميل المواد من التخزين الدائم أولاً
      final persistentSubjects = await _persistentStorage.loadSubjects();
      if (persistentSubjects.isNotEmpty) {
        _cachedSubjects = persistentSubjects;
        _subjects = _cachedSubjects;
        return _cachedSubjects;
      }

      // إذا لم توجد مواد، أرجع قائمة فارغة
      return [];
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على المواد المحفوظة: $e');
      return [];
    }
  }

  // ===== إدارة المواد =====

  /// الحصول على جميع المواد (من التخزين المحلي أولاً)
  Future<List<Subject>> getAllSubjects() async {
    try {
      // إذا كانت المواد محفوظة، أرجعها فوراً بدون تحديث تلقائي
      if (_cachedSubjects.isNotEmpty) {
        debugPrint(
          '⚡ إرجاع ${_cachedSubjects.length} مادة من التخزين المحلي فوراً',
        );
        return _cachedSubjects;
      }

      // محاولة تحميل المواد المحفوظة من Firebase أولاً
      final savedSubjects = await OfflineStorageService.instance
          .getOfflineSubjects();
      if (savedSubjects.isNotEmpty) {
        _cachedSubjects = savedSubjects;
        debugPrint(
          '⚡ تم تحميل ${_cachedSubjects.length} مادة فوراً من البيانات المحفوظة',
        );
        return _cachedSubjects;
      }

      // إذا لم توجد بيانات محفوظة، استخدم البيانات الافتراضية (الفارغة)
      _cachedSubjects = SimpleDataService.instance.getSubjects();
      debugPrint(
        '⚡ تم تحميل ${_cachedSubjects.length} مادة فوراً من البيانات المحلية',
      );
      return _cachedSubjects;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل المواد: $e');
      // إنشاء مواد افتراضية في حالة الخطأ
      _cachedSubjects = _createDefaultSubjects();
      return _cachedSubjects;
    }
  }

  /// إنشاء مواد افتراضية
  List<Subject> _createDefaultSubjects() {
    final now = DateTime.now();
    return [
      Subject(
        id: 'default_subject_math',
        name: 'الرياضيات',
        description: 'مادة الرياضيات',
        color: '#6C5CE7',
        iconUrl: '',
        sectionId: 'default_questions',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Subject(
        id: 'default_subject_physics',
        name: 'الفيزياء',
        description: 'مادة الفيزياء',
        color: '#00B894',
        iconUrl: '',
        sectionId: 'default_questions',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Subject(
        id: 'default_subject_chemistry',
        name: 'الكيمياء',
        description: 'مادة الكيمياء',
        color: '#E17055',
        iconUrl: '',
        sectionId: 'default_questions',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }

  /// تحميل المواد مباشرة من Firebase (للأدمن)
  Future<List<Subject>> loadSubjectsFromFirebase() async {
    try {
      debugPrint('🔄 تحميل المواد مباشرة من Firebase للأدمن...');

      final snapshot = await _firestore
          .collection('subjects')
          .orderBy('name')
          .get();

      final subjects = snapshot.docs
          .map((doc) => Subject.fromFirestore(doc.data(), doc.id))
          .toList();

      // طباعة تفاصيل المواد المحملة من Firebase للتشخيص
      debugPrint('🔍 المواد المحملة من Firebase (${subjects.length}):');
      for (var subject in subjects) {
        debugPrint(
          '   - ${subject.name} (sectionId: "${subject.sectionId}", isActive: ${subject.isActive})',
        );
      }

      debugPrint('✅ تم تحميل ${subjects.length} مادة من Firebase للأدمن');
      return subjects;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل المواد من Firebase: $e');
      rethrow;
    }
  }

  /// إجبار إعادة تحميل المواد من Firebase (للتحديث اليدوي)
  Future<List<Subject>> forceReloadSubjects() async {
    try {
      debugPrint('🔄 إجبار إعادة تحميل المواد من Firebase...');
      final snapshot = await _firestore
          .collection('subjects')
          .orderBy('name')
          .get();

      _cachedSubjects = snapshot.docs
          .map((doc) => Subject.fromFirestore(doc.data(), doc.id))
          .toList();
      _lastSubjectsUpdate = DateTime.now();

      // طباعة تفاصيل المواد المحملة من Firebase للتشخيص
      debugPrint('🔍 المواد المحملة من Firebase (${_cachedSubjects.length}):');
      for (var subject in _cachedSubjects) {
        debugPrint(
          '   - ${subject.name} (sectionId: "${subject.sectionId}", isActive: ${subject.isActive})',
        );
      }

      // حفظ المواد المحدثة محلياً
      if (_cachedSubjects.isNotEmpty) {
        await OfflineStorageService.instance.saveSubjects(_cachedSubjects);
        debugPrint('💾 تم حفظ ${_cachedSubjects.length} مادة محدثة محلياً');
      }

      debugPrint('✅ تم إعادة تحميل ${_cachedSubjects.length} مادة من Firebase');
      return _cachedSubjects;
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تحميل المواد: $e');
      rethrow;
    }
  }

  /// الحصول على مادة بالمعرف
  Future<Subject?> getSubjectById(String subjectId) async {
    try {
      final doc = await _firestore.collection('subjects').doc(subjectId).get();

      if (doc.exists && doc.data() != null) {
        return Subject.fromFirestore(doc.data()!, doc.id);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تحميل المادة: $e');
      return null;
    }
  }

  /// إضافة مادة جديدة
  Future<void> addSubject(Subject subject) async {
    try {
      debugPrint('➕ إضافة مادة جديدة: ${subject.name}');
      debugPrint('   - القسم: ${subject.sectionId}');
      debugPrint('   - مجانية: ${subject.isFree}');
      debugPrint('   - نشطة: ${subject.isActive}');

      final data = subject.toFirestore();
      debugPrint('📤 البيانات المحفوظة: $data');

      await _firestore.collection('subjects').doc(subject.id).set(data);
      debugPrint('✅ تم حفظ المادة في Firebase: ${subject.name}');

      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في إضافة المادة: $e');
      rethrow;
    }
  }

  /// تحديث مادة
  Future<void> updateSubject(Subject subject) async {
    try {
      debugPrint('🔄 تحديث المادة: ${subject.name}');
      debugPrint('   - القسم: ${subject.sectionId}');
      debugPrint('   - مجانية: ${subject.isFree}');
      debugPrint('   - نشطة: ${subject.isActive}');

      final data = subject.toFirestore();
      debugPrint('📤 البيانات المحدثة: $data');

      await _firestore.collection('subjects').doc(subject.id).update(data);
      debugPrint('✅ تم تحديث المادة في Firebase: ${subject.name}');

      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحديث المادة: $e');
      rethrow;
    }
  }

  /// حذف مادة
  Future<void> deleteSubject(String subjectId) async {
    try {
      // حذف جميع الوحدات والدروس والأسئلة المرتبطة بالمادة
      final batch = _firestore.batch();

      // حذف الوحدات
      final unitsSnapshot = await _firestore
          .collection('units')
          .where('subjectId', isEqualTo: subjectId)
          .get();

      for (final unitDoc in unitsSnapshot.docs) {
        batch.delete(unitDoc.reference);

        // حذف دروس الوحدة
        final lessonsSnapshot = await _firestore
            .collection('lessons')
            .where('unitId', isEqualTo: unitDoc.id)
            .get();

        for (final lessonDoc in lessonsSnapshot.docs) {
          batch.delete(lessonDoc.reference);
        }
      }

      // حذف الأسئلة
      final questionsSnapshot = await _firestore
          .collection('questions')
          .where('subjectId', isEqualTo: subjectId)
          .get();

      for (final questionDoc in questionsSnapshot.docs) {
        batch.delete(questionDoc.reference);
      }

      // حذف المادة نفسها
      batch.delete(_firestore.collection('subjects').doc(subjectId));

      await batch.commit();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في حذف المادة: $e');
      rethrow;
    }
  }

  // ===== إدارة الوحدات والدروس =====

  /// الحصول على وحدات المادة (من التخزين المحلي أولاً)
  Future<List<Unit>> getSubjectUnits(String subjectId) async {
    try {
      // محاولة تحميل الوحدات من التخزين المحلي أولاً
      final offlineUnits = await OfflineStorageService.instance
          .getOfflineUnits();
      final subjectUnits = offlineUnits
          .where((unit) => unit.subjectId == subjectId && unit.isActive)
          .toList();

      if (subjectUnits.isNotEmpty) {
        debugPrint(
          '⚡ تم تحميل ${subjectUnits.length} وحدة من التخزين المحلي للمادة $subjectId',
        );
        return subjectUnits;
      }

      // إذا لم توجد وحدات محلياً، تحميل من Firebase
      debugPrint('🔄 تحميل وحدات المادة من Firebase: $subjectId');
      final querySnapshot = await _firestore
          .collection('units')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      final units = querySnapshot.docs
          .map((doc) => Unit.fromMap(doc.data()))
          .toList();

      // حفظ الوحدات محلياً
      if (units.isNotEmpty) {
        final allUnits = await OfflineStorageService.instance.getOfflineUnits();
        final updatedUnits = allUnits
            .where((u) => u.subjectId != subjectId)
            .toList();
        updatedUnits.addAll(units);
        await OfflineStorageService.instance.saveUnits(updatedUnits);
        debugPrint('✅ تم حفظ ${units.length} وحدة محلياً للمادة $subjectId');
      }

      return units;
    } catch (e) {
      debugPrint('خطأ في تحميل الوحدات: $e');
      return [];
    }
  }

  /// الحصول على دروس الوحدة (من التخزين المحلي أولاً)
  Future<List<Lesson>> getUnitLessons(String unitId) async {
    try {
      // محاولة تحميل الدروس من التخزين المحلي أولاً
      final offlineLessons = await OfflineStorageService.instance
          .getOfflineLessons();
      final unitLessons = offlineLessons
          .where((lesson) => lesson.unitId == unitId && lesson.isActive)
          .toList();

      if (unitLessons.isNotEmpty) {
        debugPrint(
          '⚡ تم تحميل ${unitLessons.length} درس من التخزين المحلي للوحدة $unitId',
        );
        return unitLessons;
      }

      // إذا لم توجد دروس محلياً، تحميل من Firebase
      debugPrint('🔄 تحميل دروس الوحدة من Firebase: $unitId');
      final querySnapshot = await _firestore
          .collection('lessons')
          .where('unitId', isEqualTo: unitId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      final lessons = querySnapshot.docs
          .map((doc) => Lesson.fromMap(doc.data()))
          .toList();

      // حفظ الدروس محلياً
      if (lessons.isNotEmpty) {
        final allLessons = await OfflineStorageService.instance
            .getOfflineLessons();
        final updatedLessons = allLessons
            .where((l) => l.unitId != unitId)
            .toList();
        updatedLessons.addAll(lessons);
        await OfflineStorageService.instance.saveLessons(updatedLessons);
        debugPrint('✅ تم حفظ ${lessons.length} درس محلياً للوحدة $unitId');
      }

      return lessons;
    } catch (e) {
      debugPrint('خطأ في تحميل الدروس: $e');
      return [];
    }
  }

  /// الحصول على وحدة بالمعرف
  Future<Unit?> getUnitById(String unitId) async {
    try {
      final doc = await _firestore.collection('units').doc(unitId).get();

      if (doc.exists && doc.data() != null) {
        return Unit.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تحميل الوحدة: $e');
      return null;
    }
  }

  /// الحصول على درس بالمعرف
  Future<Lesson?> getLessonById(String lessonId) async {
    try {
      final doc = await _firestore.collection('lessons').doc(lessonId).get();

      if (doc.exists && doc.data() != null) {
        return Lesson.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تحميل الدرس: $e');
      return null;
    }
  }

  /// إضافة وحدة جديدة
  Future<void> addUnit(Unit unit) async {
    try {
      await _firestore.collection('units').doc(unit.id).set(unit.toMap());

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في إضافة الوحدة: $e');
      rethrow;
    }
  }

  /// تحديث وحدة
  Future<void> updateUnit(Unit unit) async {
    try {
      await _firestore.collection('units').doc(unit.id).update(unit.toMap());

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث الوحدة: $e');
      rethrow;
    }
  }

  /// حذف وحدة
  Future<void> deleteUnit(String unitId) async {
    try {
      await _firestore.collection('units').doc(unitId).delete();

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في حذف الوحدة: $e');
      rethrow;
    }
  }

  /// إضافة درس جديد
  Future<void> addLesson(Lesson lesson) async {
    try {
      await _firestore.collection('lessons').doc(lesson.id).set(lesson.toMap());

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في إضافة الدرس: $e');
      rethrow;
    }
  }

  /// تحديث درس
  Future<void> updateLesson(Lesson lesson) async {
    try {
      await _firestore
          .collection('lessons')
          .doc(lesson.id)
          .update(lesson.toMap());

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث الدرس: $e');
      rethrow;
    }
  }

  /// حذف درس
  Future<void> deleteLesson(String lessonId) async {
    try {
      await _firestore.collection('lessons').doc(lessonId).delete();

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في حذف الدرس: $e');
      rethrow;
    }
  }

  /// البحث في الوحدات
  Future<List<Unit>> searchUnits(String query, String subjectId) async {
    try {
      final querySnapshot = await _firestore
          .collection('units')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .get();

      final units = querySnapshot.docs
          .map((doc) => Unit.fromMap(doc.data()))
          .toList();

      // فلترة النتائج محلياً
      return units.where((unit) {
        return unit.name.toLowerCase().contains(query.toLowerCase()) ||
            unit.description.toLowerCase().contains(query.toLowerCase());
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في الوحدات: $e');
      return [];
    }
  }

  /// البحث في الدروس
  Future<List<Lesson>> searchLessons(String query, String unitId) async {
    try {
      final querySnapshot = await _firestore
          .collection('lessons')
          .where('unitId', isEqualTo: unitId)
          .where('isActive', isEqualTo: true)
          .get();

      final lessons = querySnapshot.docs
          .map((doc) => Lesson.fromMap(doc.data()))
          .toList();

      // فلترة النتائج محلياً
      return lessons.where((lesson) {
        return lesson.name.toLowerCase().contains(query.toLowerCase()) ||
            lesson.description.toLowerCase().contains(query.toLowerCase());
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في الدروس: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات المحتوى
  Future<Map<String, int>> getContentStats(String subjectId) async {
    try {
      final unitsSnapshot = await _firestore
          .collection('units')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .get();

      int totalLessons = 0;
      for (final unitDoc in unitsSnapshot.docs) {
        final lessonsSnapshot = await _firestore
            .collection('lessons')
            .where('unitId', isEqualTo: unitDoc.id)
            .where('isActive', isEqualTo: true)
            .get();
        totalLessons += lessonsSnapshot.docs.length;
      }

      return {'units': unitsSnapshot.docs.length, 'lessons': totalLessons};
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات المحتوى: $e');
      return {'units': 0, 'lessons': 0};
    }
  }

  /// تفعيل/إلغاء تفعيل وحدة
  Future<void> toggleUnitStatus(String unitId, bool isActive) async {
    try {
      await _firestore.collection('units').doc(unitId).update({
        'isActive': isActive,
        'updatedAt': Timestamp.now(),
      });

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تغيير حالة الوحدة: $e');
      rethrow;
    }
  }

  /// تفعيل/إلغاء تفعيل درس
  Future<void> toggleLessonStatus(String lessonId, bool isActive) async {
    try {
      await _firestore.collection('lessons').doc(lessonId).update({
        'isActive': isActive,
        'updatedAt': Timestamp.now(),
      });

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تغيير حالة الدرس: $e');
      rethrow;
    }
  }

  /// الحصول على الوحدات حسب المادة
  Future<List<Unit>> getUnitsBySubject(String subjectId) async {
    try {
      final querySnapshot = await _firestore
          .collection('units')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return querySnapshot.docs
          .map((doc) => Unit.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل الوحدات: $e');
      return [];
    }
  }

  /// الحصول على الدروس حسب المادة
  Future<List<Lesson>> getLessonsBySubject(String subjectId) async {
    try {
      final querySnapshot = await _firestore
          .collection('lessons')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return querySnapshot.docs
          .map((doc) => Lesson.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل الدروس: $e');
      return [];
    }
  }
}
