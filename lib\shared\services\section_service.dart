import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../core/models/section.dart';
import 'offline_storage_service.dart';
import 'simple_data_service.dart';
import 'persistent_storage_service.dart';

class SectionService extends ChangeNotifier {
  static final SectionService _instance = SectionService._internal();
  static SectionService get instance => _instance;
  SectionService._internal() {
    // سيتم تحميل البيانات في initialize() مثل VideoService
  }

  /// تهيئة الخدمة وتحميل البيانات المحفوظة (مثل VideoService.initialize)
  Future<void> initialize() async {
    if (_isInitialized) return; // تجنب التحميل المتكرر

    try {
      debugPrint('📱 تحميل أقسام الاختبارات المحفوظة دائمياً...');

      // تحميل البيانات المحفوظة دائمياً (مثل VideoService)
      _cachedSections = await _persistentStorage.loadSections();

      // إذا لم تكن هناك أقسام محفوظة، استخدم البيانات الافتراضية
      if (_cachedSections.isEmpty) {
        _cachedSections = _createDefaultSections();
        debugPrint(
          '📱 تم تحميل ${_cachedSections.length} قسم من البيانات الافتراضية',
        );
      } else {
        debugPrint(
          '📱 تم تحميل ${_cachedSections.length} قسم من التخزين الدائم',
        );
      }

      // تحديث _sections للتوافق مع الكود القديم
      _sections = _cachedSections;

      debugPrint(
        '✅ تم تحميل البيانات الدائمة: ${_cachedSections.length} أقسام',
      );
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات المحفوظة: $e');
      _cachedSections = _createDefaultSections();
      _sections = _cachedSections;
    } finally {
      _isInitialized = true; // تم انتهاء التحميل الأولي
    }
  }

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final PersistentStorageService _persistentStorage =
      PersistentStorageService.instance;

  // تخزين مؤقت للبيانات (كاش في الذاكرة مثل VideoService)
  List<Section> _cachedSections = [];
  List<Section> _sections = []; // للتوافق مع الكود القديم
  bool _isInitialized = false; // للتأكد من انتهاء التحميل الأولي

  List<Section> get sections => _sections;
  List<Section> get activeSections =>
      _sections.where((s) => s.isActive).toList();

  /// الحصول على الأقسام المجانية النشطة فقط
  List<Section> get freeSections =>
      _sections.where((s) => s.isActive && s.isFree).toList();

  /// الحصول على الأقسام المدفوعة النشطة فقط (مثل VideoService)
  List<Section> get paidSections =>
      _sections.where((s) => s.isActive && !s.isFree).toList();

  /// الحصول على الأقسام المدفوعة فقط (بدون تحديث تلقائي) - مثل getPaidVideoSections
  Future<List<Section>> getPaidSections() async {
    try {
      // إذا كانت الأقسام محفوظة في الكاش، أرجعها فوراً (مثل VideoService)
      if (_cachedSections.isNotEmpty) {
        final paidSections = _cachedSections
            .where((s) => !s.isFree && s.isActive)
            .toList();
        debugPrint('⚡ إرجاع ${paidSections.length} قسم مدفوع من الكاش فوراً');
        return paidSections;
      }

      // محاولة تحميل الأقسام من التخزين الدائم أولاً
      final persistentSections = await _persistentStorage.loadSections();
      if (persistentSections.isNotEmpty) {
        _cachedSections = persistentSections;
        _sections = persistentSections; // للتوافق مع الكود القديم
        final paidSections = persistentSections
            .where((s) => !s.isFree && s.isActive)
            .toList();
        debugPrint(
          '⚡ تم تحميل ${paidSections.length} قسم مدفوع من التخزين الدائم',
        );
        return paidSections;
      }

      // إذا لم توجد في التخزين الدائم، حمل من التخزين المحلي القديم
      final offlineSections = await OfflineStorageService.instance
          .getOfflineSections();

      if (offlineSections.isNotEmpty) {
        // حفظ في الكاش للمرات القادمة
        _cachedSections = offlineSections;
        _sections = offlineSections; // للتوافق مع الكود القديم
        // حفظ في التخزين الدائم أيضاً
        await _persistentStorage.saveSections(_cachedSections);
        final paidSections = offlineSections
            .where((s) => !s.isFree && s.isActive)
            .toList();
        debugPrint(
          '⚡ تم تحميل ${paidSections.length} قسم مدفوع من التخزين المحلي القديم',
        );
        return paidSections;
      }

      // إذا لم توجد بيانات، أرجع قائمة فارغة
      debugPrint('📭 لا توجد أقسام محفوظة');
      return [];
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الأقسام المدفوعة: $e');
      return [];
    }
  }

  /// تحميل جميع الأقسام (بيانات محلية فورية!)
  Future<void> loadSections() async {
    try {
      // الأقسام محملة مسبقاً من التخزين الدائم في _loadPersistentData
      // هذه الدالة موجودة للتوافق مع الكود القديم
      if (_sections.isNotEmpty) {
        debugPrint(
          '📚 الأقسام محملة مسبقاً (${_sections.length} قسم) - تخطي التحميل',
        );
        return;
      }

      // إذا لم تكن محملة لسبب ما، حمّلها من التخزين الدائم
      await initialize();
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الأقسام: $e');
      // إنشاء أقسام افتراضية في حالة الخطأ
      _cachedSections = _createDefaultSections();
      _sections = _cachedSections;
      notifyListeners();
    }
  }

  /// إنشاء أقسام افتراضية
  List<Section> _createDefaultSections() {
    final now = DateTime.now();
    return [
      Section(
        id: 'default_questions',
        name: 'الأسئلة',
        description: 'قسم الأسئلة والاختبارات',
        color: '#6C5CE7',
        isFree: false,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Section(
        id: 'default_videos',
        name: 'الفيديوهات',
        description: 'قسم الفيديوهات التعليمية',
        color: '#00B894',
        isFree: false,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Section(
        id: 'default_courses',
        name: 'الكورسات',
        description: 'قسم الكورسات المتكاملة',
        color: '#E17055',
        isFree: false,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Section(
        id: 'default_trial',
        name: 'التجريبي',
        description: 'المحتوى التجريبي المجاني',
        color: '#FDCB6E',
        isFree: true,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }

  /// تحديث الأقسام من Firebase (فقط عند الطلب اليدوي)
  Future<bool> updateSectionsFromFirebase() async {
    try {
      debugPrint('🔄 تحديث الأقسام من Firebase...');

      // إضافة timeout لتجنب الانتظار الطويل
      final querySnapshot = await _firestore
          .collection('sections')
          .orderBy('createdAt', descending: false)
          .get()
          .timeout(
            const Duration(seconds: 15),
            onTimeout: () {
              debugPrint('⏰ انتهت مهلة تحديث الأقسام من Firebase');
              throw Exception('انتهت مهلة الاتصال بالخادم');
            },
          );

      final newSections = querySnapshot.docs
          .map((doc) => Section.fromFirestore(doc))
          .toList();

      if (newSections.isNotEmpty) {
        // تحديث الكاش أولاً (مثل VideoService)
        _cachedSections = newSections;
        _sections = newSections; // للتوافق مع الكود القديم
        // حفظ الأقسام الجديدة محلياً وفي التخزين الدائم
        await OfflineStorageService.instance.saveSections(_cachedSections);
        await _persistentStorage.saveSections(_cachedSections);
        debugPrint(
          '💾 تم حفظ ${_cachedSections.length} قسم محدث محلياً وفي التخزين الدائم',
        );
        notifyListeners();
        debugPrint('✅ تم تحديث ${_cachedSections.length} قسم من Firebase');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث الأقسام: $e');
      return false;
    }
  }

  /// إجبار إعادة تحميل الأقسام (للتوافق مع الكود القديم)
  Future<void> forceReloadSections() async {
    await updateSectionsFromFirebase();
  }

  /// الحصول على قسم بالمعرف
  Future<Section?> getSectionById(String sectionId) async {
    try {
      final doc = await _firestore.collection('sections').doc(sectionId).get();

      if (doc.exists && doc.data() != null) {
        return Section.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تحميل القسم: $e');
      return null;
    }
  }

  /// إضافة قسم جديد
  Future<void> addSection(Section section) async {
    try {
      await _firestore
          .collection('sections')
          .doc(section.id)
          .set(section.toFirestore());
      await loadSections(); // إعادة تحميل الأقسام
    } catch (e) {
      debugPrint('خطأ في إضافة القسم: $e');
      rethrow;
    }
  }

  /// تحديث قسم
  Future<void> updateSection(Section section) async {
    try {
      await _firestore
          .collection('sections')
          .doc(section.id)
          .update(section.copyWith(updatedAt: DateTime.now()).toFirestore());
      await loadSections(); // إعادة تحميل الأقسام
    } catch (e) {
      debugPrint('خطأ في تحديث القسم: $e');
      rethrow;
    }
  }

  /// حذف قسم
  Future<void> deleteSection(String sectionId) async {
    try {
      await _firestore.collection('sections').doc(sectionId).delete();
      await loadSections(); // إعادة تحميل الأقسام
    } catch (e) {
      debugPrint('خطأ في حذف القسم: $e');
      rethrow;
    }
  }

  /// تفعيل/إلغاء تفعيل قسم
  Future<void> toggleSectionStatus(String sectionId) async {
    try {
      final section = _sections.firstWhere((s) => s.id == sectionId);
      final updatedSection = section.copyWith(
        isActive: !section.isActive,
        updatedAt: DateTime.now(),
      );

      await updateSection(updatedSection);
    } catch (e) {
      debugPrint('خطأ في تغيير حالة القسم: $e');
      rethrow;
    }
  }

  /// البحث في الأقسام
  List<Section> searchSections(String query) {
    if (query.isEmpty) return activeSections;

    return activeSections.where((section) {
      return section.name.toLowerCase().contains(query.toLowerCase()) ||
          section.description.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  /// الحصول على عدد المواد في كل قسم
  Future<Map<String, int>> getSectionSubjectCounts() async {
    try {
      final Map<String, int> counts = {};

      for (final section in _sections) {
        final querySnapshot = await _firestore
            .collection('subjects')
            .where('sectionId', isEqualTo: section.id)
            .where('isActive', isEqualTo: true)
            .get();

        counts[section.id] = querySnapshot.docs.length;
      }

      return counts;
    } catch (e) {
      debugPrint('خطأ في حساب عدد المواد: $e');
      return {};
    }
  }
}
