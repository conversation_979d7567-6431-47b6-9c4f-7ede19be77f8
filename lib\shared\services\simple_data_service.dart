import 'package:flutter/foundation.dart';
import '../../core/models/section.dart';
import '../models/subject_model.dart';
import '../models/video_section_model.dart';
import '../models/video_subject_model.dart';
import '../models/video_unit_model.dart';
import '../models/video_lesson_model.dart';
import '../models/video_model.dart';

/// خدمة بيانات بسيطة تعمل بدون Firebase
class SimpleDataService {
  static final SimpleDataService _instance = SimpleDataService._internal();
  factory SimpleDataService() => _instance;
  SimpleDataService._internal();

  static SimpleDataService get instance => _instance;

  // البيانات المحلية
  List<Section>? _sections;
  List<Subject>? _subjects;
  List<VideoSection>? _videoSections;
  List<VideoSubject>? _videoSubjects;
  List<VideoUnit>? _videoUnits;
  List<VideoLesson>? _videoLessons;
  List<Video>? _videos;

  /// الحصول على الأقسام
  List<Section> getSections() {
    _sections ??= _createDefaultSections();
    debugPrint('⚡ تم إرجاع ${_sections!.length} قسم فوراً');
    return _sections!;
  }

  /// الحصول على المواد
  List<Subject> getSubjects() {
    _subjects ??= _createDefaultSubjects();
    debugPrint('⚡ تم إرجاع ${_subjects!.length} مادة فوراً');
    return _subjects!;
  }

  /// الحصول على أقسام الفيديو
  List<VideoSection> getVideoSections() {
    _videoSections ??= _createDefaultVideoSections();
    debugPrint('⚡ تم إرجاع ${_videoSections!.length} قسم فيديو فوراً');
    return _videoSections!;
  }

  /// الحصول على مواد الفيديو
  List<VideoSubject> getVideoSubjects() {
    _videoSubjects ??= _createDefaultVideoSubjects();
    debugPrint('⚡ تم إرجاع ${_videoSubjects!.length} مادة فيديو فوراً');
    return _videoSubjects!;
  }

  /// الحصول على وحدات الفيديو
  List<VideoUnit> getVideoUnits() {
    _videoUnits ??= _createDefaultVideoUnits();
    debugPrint('⚡ تم إرجاع ${_videoUnits!.length} وحدة فيديو فوراً');
    return _videoUnits!;
  }

  /// الحصول على دروس الفيديو
  List<VideoLesson> getVideoLessons() {
    _videoLessons ??= _createDefaultVideoLessons();
    debugPrint('⚡ تم إرجاع ${_videoLessons!.length} درس فيديو فوراً');
    return _videoLessons!;
  }

  /// الحصول على الفيديوهات
  List<Video> getVideos() {
    _videos ??= _createDefaultVideos();
    debugPrint('⚡ تم إرجاع ${_videos!.length} فيديو فوراً');
    return _videos!;
  }

  /// إنشاء أقسام افتراضية
  List<Section> _createDefaultSections() {
    // إرجاع قائمة فارغة - سيتم الاعتماد على البيانات المحفوظة من Firebase فقط
    return [];
  }

  /// إنشاء مواد افتراضية
  List<Subject> _createDefaultSubjects() {
    // إرجاع قائمة فارغة - سيتم الاعتماد على البيانات المحفوظة من Firebase فقط
    return [];
  }

  /// إنشاء أقسام فيديو افتراضية
  List<VideoSection> _createDefaultVideoSections() {
    final now = DateTime.now();
    return [
      VideoSection(
        id: 'math_videos',
        name: 'فيديوهات الرياضيات',
        description: 'فيديوهات تعليمية للرياضيات',
        color: '#6C5CE7',
        order: 1,
        isFree: false,
        isActive: true,
        createdAt: now,
        updatedAt: now,
        createdByAdminId: 'admin',
      ),
    ];
  }

  /// إنشاء مواد فيديو افتراضية
  List<VideoSubject> _createDefaultVideoSubjects() {
    final now = DateTime.now();
    return [
      VideoSubject(
        id: 'math_video_subject',
        name: 'الرياضيات',
        description: 'مواد فيديو الرياضيات',
        color: '#6C5CE7',
        order: 1,
        sectionId: 'math_videos',
        isActive: true,
        createdAt: now,
        updatedAt: now,
        createdByAdminId: 'admin',
      ),
    ];
  }

  /// إنشاء وحدات فيديو افتراضية
  List<VideoUnit> _createDefaultVideoUnits() {
    // إرجاع قائمة فارغة - سيتم الاعتماد على البيانات المحفوظة من Firebase فقط
    return [];
  }

  /// إنشاء دروس فيديو افتراضية
  List<VideoLesson> _createDefaultVideoLessons() {
    // إرجاع قائمة فارغة - سيتم الاعتماد على البيانات المحفوظة من Firebase فقط
    return [];
  }

  /// إنشاء فيديوهات افتراضية
  List<Video> _createDefaultVideos() {
    // إرجاع قائمة فارغة - سيتم الاعتماد على البيانات المحفوظة من Firebase فقط
    return [];
  }

  /// مسح البيانات المؤقتة
  void clearCache() {
    _sections = null;
    _subjects = null;
    _videoSections = null;
    _videoSubjects = null;
    _videoUnits = null;
    _videoLessons = null;
    _videos = null;
    debugPrint('🗑️ تم مسح البيانات المؤقتة');
  }
}
