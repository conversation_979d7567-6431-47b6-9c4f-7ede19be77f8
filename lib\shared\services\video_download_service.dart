import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';
import '../models/video_model.dart';

/// خدمة تحميل الفيديوهات مع التشفير والحماية المتقدمة
class VideoDownloadService {
  static final VideoDownloadService _instance =
      VideoDownloadService._internal();
  static VideoDownloadService get instance => _instance;
  VideoDownloadService._internal();

  final Dio _dio = Dio();
  final Map<String, CancelToken> _downloadTokens = {};
  final Map<String, bool> _downloadedVideos = {};

  /// تحميل فيديو بجودة محددة مع مؤشر التقدم
  Future<void> downloadVideo(
    Video video,
    String quality, {
    required Function(double) onProgress,
  }) async {
    try {
      // الحصول على رابط الفيديو حسب الجودة
      final videoUrl = _getVideoUrlByQuality(video, quality);
      if (videoUrl == null) {
        throw Exception('رابط الفيديو غير متوفر للجودة المطلوبة');
      }

      // إنشاء مجلد التحميل المشفر
      final downloadDir = await _getSecureDownloadDirectory();
      final fileName = _generateSecureFileName(video.id, quality);
      final filePath = '${downloadDir.path}/$fileName';

      // إنشاء token للإلغاء
      final cancelToken = CancelToken();
      _downloadTokens[video.id] = cancelToken;

      debugPrint('📥 بدء تحميل الفيديو: ${video.title} - جودة: $quality');

      // تحميل الفيديو مع التشفير
      await _dio.download(
        videoUrl,
        filePath,
        cancelToken: cancelToken,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = received / total;
            onProgress(progress);
            debugPrint('📊 تقدم التحميل: ${(progress * 100).toInt()}%');
          }
        },
      );

      // تشفير الملف المحمل
      await _encryptVideoFile(filePath);

      // حفظ معلومات الفيديو المحمل
      await _saveVideoMetadata(video, quality, filePath);

      // تحديث حالة التحميل
      _downloadedVideos[video.id] = true;
      _downloadTokens.remove(video.id);

      debugPrint('✅ تم تحميل وتشفير الفيديو بنجاح: ${video.title}');
    } catch (e) {
      _downloadTokens.remove(video.id);
      if (e is DioException && e.type == DioExceptionType.cancel) {
        debugPrint('🚫 تم إلغاء تحميل الفيديو: ${video.title}');
        throw Exception('تم إلغاء التحميل');
      } else {
        debugPrint('❌ خطأ في تحميل الفيديو: $e');
        throw Exception('فشل في تحميل الفيديو: $e');
      }
    }
  }

  /// إلغاء تحميل فيديو
  void cancelDownload(String videoId) {
    final cancelToken = _downloadTokens[videoId];
    if (cancelToken != null && !cancelToken.isCancelled) {
      cancelToken.cancel('تم إلغاء التحميل من قبل المستخدم');
      _downloadTokens.remove(videoId);
      debugPrint('🚫 تم إلغاء تحميل الفيديو: $videoId');
    }
  }

  /// التحقق من تحميل الفيديو
  bool isVideoDownloaded(String videoId) {
    return _downloadedVideos[videoId] ?? false;
  }

  /// الحصول على مسار الفيديو المحلي
  Future<String?> getLocalVideoPath(String videoId) async {
    if (!isVideoDownloaded(videoId)) return null;

    try {
      final downloadDir = await _getSecureDownloadDirectory();
      final files = downloadDir.listSync();

      for (final file in files) {
        if (file.path.contains(videoId)) {
          // فك تشفير الملف وإرجاع المسار المؤقت
          return await _decryptVideoFile(file.path);
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على مسار الفيديو المحلي: $e');
    }

    return null;
  }

  /// حذف فيديو محمل
  Future<void> deleteDownloadedVideo(String videoId) async {
    try {
      final downloadDir = await _getSecureDownloadDirectory();
      final files = downloadDir.listSync();

      for (final file in files) {
        if (file.path.contains(videoId)) {
          await file.delete();
          _downloadedVideos.remove(videoId);
          debugPrint('🗑️ تم حذف الفيديو المحمل: $videoId');
          break;
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في حذف الفيديو: $e');
    }
  }

  /// الحصول على حجم جميع الفيديوهات المحملة
  Future<int> getTotalDownloadedSize() async {
    try {
      final downloadDir = await _getSecureDownloadDirectory();
      final files = downloadDir.listSync();
      int totalSize = 0;

      for (final file in files) {
        if (file is File) {
          totalSize += await file.length();
        }
      }

      return totalSize;
    } catch (e) {
      debugPrint('❌ خطأ في حساب حجم الفيديوهات: $e');
      return 0;
    }
  }

  /// الحصول على قائمة الفيديوهات المحملة
  Future<List<String>> getDownloadedVideoIds() async {
    try {
      final downloadDir = await _getSecureDownloadDirectory();
      final files = downloadDir.listSync();
      final videoIds = <String>[];

      for (final file in files) {
        final fileName = file.path.split('/').last;
        if (fileName.contains('_video_')) {
          final videoId = fileName.split('_video_')[0];
          videoIds.add(videoId);
        }
      }

      return videoIds;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على قائمة الفيديوهات: $e');
      return [];
    }
  }

  /// تحديث حالة الفيديوهات المحملة عند بدء التطبيق
  Future<void> loadDownloadedVideosState() async {
    try {
      final videoIds = await getDownloadedVideoIds();
      for (final videoId in videoIds) {
        _downloadedVideos[videoId] = true;
      }
      debugPrint('📱 تم تحميل حالة ${videoIds.length} فيديو محمل');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل حالة الفيديوهات: $e');
    }
  }

  // الدوال المساعدة الخاصة

  String? _getVideoUrlByQuality(Video video, String quality) {
    switch (quality) {
      case '720p':
        return video.encryptedUrl720;
      case '480p':
        return video.encryptedUrl480;
      case '360p':
        return video.encryptedUrl360;
      default:
        return video.encryptedUrl480 ??
            video.encryptedUrl360 ??
            video.encryptedUrl720;
    }
  }

  Future<Directory> _getSecureDownloadDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final downloadDir = Directory('${appDir.path}/secure_videos');

    if (!await downloadDir.exists()) {
      await downloadDir.create(recursive: true);
    }

    return downloadDir;
  }

  String _generateSecureFileName(String videoId, String quality) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final hash = sha256
        .convert('$videoId$quality$timestamp'.codeUnits)
        .toString();
    return '${videoId}_video_${quality}_$hash.enc';
  }

  Future<void> _encryptVideoFile(String filePath) async {
    try {
      final file = File(filePath);
      final bytes = await file.readAsBytes();

      // تشفير بسيط (يمكن تحسينه بخوارزميات أقوى)
      final encryptedBytes = _simpleEncrypt(bytes);

      await file.writeAsBytes(encryptedBytes);
      debugPrint('🔒 تم تشفير الفيديو: $filePath');
    } catch (e) {
      debugPrint('❌ خطأ في تشفير الفيديو: $e');
      throw Exception('فشل في تشفير الفيديو');
    }
  }

  Future<String> _decryptVideoFile(String encryptedPath) async {
    try {
      final encryptedFile = File(encryptedPath);
      final encryptedBytes = await encryptedFile.readAsBytes();

      // فك التشفير
      final decryptedBytes = _simpleDecrypt(encryptedBytes);

      // إنشاء ملف مؤقت
      final tempDir = await getTemporaryDirectory();
      final tempPath =
          '${tempDir.path}/temp_video_${DateTime.now().millisecondsSinceEpoch}.mp4';
      final tempFile = File(tempPath);

      await tempFile.writeAsBytes(decryptedBytes);
      debugPrint('🔓 تم فك تشفير الفيديو: $tempPath');

      return tempPath;
    } catch (e) {
      debugPrint('❌ خطأ في فك تشفير الفيديو: $e');
      throw Exception('فشل في فك تشفير الفيديو');
    }
  }

  Uint8List _simpleEncrypt(Uint8List data) {
    // تشفير بسيط بـ XOR (يمكن استخدام AES للحماية الأقوى)
    const key = 0xAB;
    final encrypted = Uint8List(data.length);
    for (int i = 0; i < data.length; i++) {
      encrypted[i] = data[i] ^ key;
    }
    return encrypted;
  }

  Uint8List _simpleDecrypt(Uint8List encryptedData) {
    // فك التشفير (نفس العملية مع XOR)
    return _simpleEncrypt(encryptedData);
  }

  Future<void> _saveVideoMetadata(
    Video video,
    String quality,
    String filePath,
  ) async {
    try {
      final downloadDir = await _getSecureDownloadDirectory();
      final metadataPath = '${downloadDir.path}/${video.id}_metadata.json';
      final metadataFile = File(metadataPath);

      final metadata = {
        'videoId': video.id,
        'title': video.title,
        'quality': quality,
        'downloadDate': DateTime.now().toIso8601String(),
        'filePath': filePath,
        'fileSize': await File(filePath).length(),
      };

      await metadataFile.writeAsString(metadata.toString());
      debugPrint('💾 تم حفظ معلومات الفيديو: ${video.title}');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ معلومات الفيديو: $e');
    }
  }
}
