import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/video_section_model.dart';
import '../models/video_subject_model.dart';
import '../models/video_unit_model.dart';
import '../models/video_lesson_model.dart';
import '../models/video_model.dart';
import 'simple_data_service.dart';
import 'encryption_service.dart';
import 'device_service.dart';
import 'offline_storage_service.dart';
import 'persistent_storage_service.dart';

/// خدمة إدارة الفيديوهات مع التشفير والحماية
class VideoService extends ChangeNotifier {
  static final VideoService _instance = VideoService._internal();
  static VideoService get instance => _instance;
  VideoService._internal();

  late final FirebaseFirestore _firestore;
  final EncryptionService _encryption = EncryptionService.instance;
  final PersistentStorageService _persistentStorage =
      PersistentStorageService.instance;
  final Dio _dio = Dio();
  final Map<String, CancelToken> _downloadTokens = {};

  // Getter للوصول للتخزين الدائم
  PersistentStorageService get persistentStorage => _persistentStorage;

  // مجلد حفظ الفيديوهات المشفرة
  Directory? _videosDirectory;

  // تخزين مؤقت للبيانات (كاش في الذاكرة مثل ContentService)
  List<VideoSection> _cachedVideoSections = [];
  List<VideoSubject> _cachedVideoSubjects = [];
  List<VideoUnit> _cachedVideoUnits = [];
  List<VideoLesson> _cachedVideoLessons = [];
  List<Video> _cachedVideos = [];

  DateTime? _lastSectionsUpdate;
  DateTime? _lastSubjectsUpdate;
  DateTime? _lastUnitsUpdate;
  DateTime? _lastLessonsUpdate;
  DateTime? _lastVideosUpdate;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    _firestore = FirebaseFirestore.instance;
    _encryption.initialize();
    await _persistentStorage.initialize();
    await _initializeVideosDirectory();
    await _cleanupOrphanedVideoInfo();

    // تحميل البيانات المحفوظة دائمياً
    await _loadPersistentData();
  }

  /// تحميل البيانات المحفوظة دائمياً
  Future<void> _loadPersistentData() async {
    try {
      debugPrint('📱 تحميل البيانات المحفوظة دائمياً...');

      _cachedVideoSections = await _persistentStorage.loadVideoSections();
      _cachedVideoSubjects = await _persistentStorage.loadVideoSubjects();
      _cachedVideoUnits = await _persistentStorage.loadVideoUnits();
      _cachedVideoLessons = await _persistentStorage.loadVideoLessons();
      _cachedVideos = await _persistentStorage.loadVideos();

      // إذا لم تكن هناك بيانات محفوظة، استخدم البيانات الافتراضية للأقسام والمواد فقط
      if (_cachedVideoSections.isEmpty) {
        _cachedVideoSections = SimpleDataService.instance.getVideoSections();
        debugPrint(
          '📱 تم تحميل ${_cachedVideoSections.length} قسم فيديو من البيانات الافتراضية',
        );
      }

      if (_cachedVideoSubjects.isEmpty) {
        _cachedVideoSubjects = SimpleDataService.instance.getVideoSubjects();
        debugPrint(
          '📱 تم تحميل ${_cachedVideoSubjects.length} مادة فيديو من البيانات الافتراضية',
        );
      }

      // إذا لم تكن هناك بيانات محفوظة للوحدات والدروس والفيديوهات،
      // حاول تحميلها من البيانات الافتراضية (التي ستكون فارغة)
      if (_cachedVideoUnits.isEmpty) {
        _cachedVideoUnits = SimpleDataService.instance.getVideoUnits();
        debugPrint(
          '📱 تم تحميل ${_cachedVideoUnits.length} وحدة فيديو من البيانات الافتراضية',
        );
      }

      if (_cachedVideoLessons.isEmpty) {
        _cachedVideoLessons = SimpleDataService.instance.getVideoLessons();
        debugPrint(
          '📱 تم تحميل ${_cachedVideoLessons.length} درس فيديو من البيانات الافتراضية',
        );
      }

      if (_cachedVideos.isEmpty) {
        _cachedVideos = SimpleDataService.instance.getVideos();
        debugPrint(
          '📱 تم تحميل ${_cachedVideos.length} فيديو من البيانات الافتراضية',
        );
      }

      debugPrint(
        '✅ تم تحميل البيانات الدائمة: ${_cachedVideoSections.length} أقسام، ${_cachedVideoSubjects.length} مواد، ${_cachedVideoUnits.length} وحدات، ${_cachedVideoLessons.length} دروس، ${_cachedVideos.length} فيديو',
      );
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات الدائمة: $e');
    }
  }

  /// تهيئة مجلد الفيديوهات
  Future<void> _initializeVideosDirectory() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _videosDirectory = Directory('${appDir.path}/encrypted_videos');

      if (!await _videosDirectory!.exists()) {
        await _videosDirectory!.create(recursive: true);
      }
    } catch (e) {
      debugPrint('خطأ في تهيئة مجلد الفيديوهات: $e');
    }
  }

  // ===== إدارة أقسام الفيديوهات =====

  /// الحصول على جميع أقسام الفيديوهات
  Future<List<VideoSection>> getVideoSections() async {
    try {
      final snapshot = await _firestore
          .collection('video_sections')
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return snapshot.docs
          .map((doc) => VideoSection.fromFirestore(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل أقسام الفيديوهات: $e');
      return [];
    }
  }

  /// الحصول على الأقسام المجانية فقط (من التخزين المحلي أولاً)
  Future<List<VideoSection>> getFreeVideoSections() async {
    try {
      // محاولة تحميل الأقسام من التخزين المحلي أولاً
      if (_cachedVideoSections.isNotEmpty) {
        final freeSections = _cachedVideoSections
            .where((s) => s.isFree)
            .toList();
        debugPrint(
          '⚡ تم تحميل ${freeSections.length} قسم فيديو مجاني من التخزين المحلي',
        );
        return freeSections;
      }

      // إذا لم توجد أقسام محلياً، تحميل من Firebase
      debugPrint('🔄 تحميل أقسام الفيديوهات المجانية من Firebase');
      final snapshot = await _firestore
          .collection('video_sections')
          .where('isActive', isEqualTo: true)
          .get();

      _cachedVideoSections = snapshot.docs
          .map((doc) => VideoSection.fromFirestore(doc.data(), doc.id))
          .toList();

      final freeSections = _cachedVideoSections.where((s) => s.isFree).toList();

      // ترتيب الأقسام حسب الترتيب
      freeSections.sort((a, b) => a.order.compareTo(b.order));

      debugPrint(
        '✅ تم تحميل ${freeSections.length} قسم فيديو مجاني من Firebase',
      );
      return freeSections;
    } catch (e) {
      debugPrint('خطأ في تحميل الأقسام المجانية: $e');
      return [];
    }
  }

  /// تهيئة البيانات الأساسية (تحميل من Firebase إذا لم تكن محفوظة محلياً)
  Future<void> initializeVideoData() async {
    try {
      debugPrint('🚀 تهيئة بيانات الفيديوهات...');

      // فحص وجود أقسام الفيديوهات محلياً
      final localSections = await OfflineStorageService.instance
          .getOfflineVideoSections();

      if (localSections.isEmpty) {
        debugPrint('📥 لا توجد أقسام فيديوهات محلية - تحميل من Firebase...');
        await refreshVideoSectionsFromFirebase();
      } else {
        debugPrint('✅ توجد ${localSections.length} قسم فيديو محفوظ محلياً');
      }

      // فحص وجود مواد الفيديوهات محلياً
      final localSubjects = await OfflineStorageService.instance
          .getOfflineVideoSubjects();

      if (localSubjects.isEmpty) {
        debugPrint('📥 لا توجد مواد فيديوهات محلية - تحميل من Firebase...');
        // تحميل مواد جميع الأقسام
        for (final section
            in localSections.isNotEmpty
                ? localSections
                : await refreshVideoSectionsFromFirebase()) {
          await refreshVideoSubjectsFromFirebase(section.id);
        }
      } else {
        debugPrint('✅ توجد ${localSubjects.length} مادة فيديو محفوظة محلياً');
      }

      debugPrint('🎉 تم الانتهاء من تهيئة بيانات الفيديوهات');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة بيانات الفيديوهات: $e');
    }
  }

  /// تحديث أقسام الفيديوهات من Firebase (للاستخدام في الخلفية)
  Future<List<VideoSection>> refreshVideoSectionsFromFirebase() async {
    try {
      debugPrint('🔄 تحديث أقسام الفيديوهات من Firebase...');

      final querySnapshot = await _firestore
          .collection('video_sections')
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      final sections = querySnapshot.docs
          .map((doc) => VideoSection.fromFirestore(doc.data(), doc.id))
          .toList();

      if (sections.isNotEmpty) {
        // حفظ في التخزين الدائم أولاً
        await _persistentStorage.saveVideoSections(sections);
        await _persistentStorage.saveLastUpdate('video_sections');

        // حفظ في التخزين المحلي القديم أيضاً للتوافق
        await OfflineStorageService.instance.saveVideoSections(sections);

        // تحديث الكاش
        _cachedVideoSections = sections;
        _lastSectionsUpdate = DateTime.now();

        debugPrint(
          '✅ تم تحديث وحفظ ${sections.length} قسم فيديو من Firebase في التخزين الدائم',
        );
      }

      return sections;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث أقسام الفيديوهات من Firebase: $e');
      rethrow;
    }
  }

  /// تحديث مواد الفيديوهات من Firebase (للاستخدام في الخلفية)
  Future<List<VideoSubject>> refreshVideoSubjectsFromFirebase(
    String sectionId,
  ) async {
    try {
      debugPrint('🔄 تحديث مواد الفيديوهات من Firebase للقسم: $sectionId');

      final querySnapshot = await _firestore
          .collection('video_subjects')
          .where('sectionId', isEqualTo: sectionId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      final subjects = querySnapshot.docs
          .map((doc) => VideoSubject.fromFirestore(doc.data(), doc.id))
          .toList();

      if (subjects.isNotEmpty) {
        // حفظ في التخزين الدائم أولاً
        await _persistentStorage.saveVideoSubjects(subjects);
        await _persistentStorage.saveLastUpdate('video_subjects');

        // حفظ في التخزين المحلي القديم أيضاً للتوافق
        await OfflineStorageService.instance.saveVideoSubjects(subjects);

        // تحديث الكاش
        _cachedVideoSubjects = subjects;
        _lastSubjectsUpdate = DateTime.now();

        debugPrint(
          '✅ تم تحديث وحفظ ${subjects.length} مادة فيديو من Firebase في التخزين الدائم',
        );
      }

      return subjects;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث مواد الفيديوهات من Firebase: $e');
      rethrow;
    }
  }

  /// تحديث وحدات الفيديوهات من Firebase (للاستخدام في الخلفية)
  Future<List<VideoUnit>> refreshVideoUnitsFromFirebase(
    String subjectId,
  ) async {
    try {
      debugPrint('🔄 تحديث وحدات الفيديوهات من Firebase للمادة: $subjectId');

      final querySnapshot = await _firestore
          .collection('video_units')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      final units = querySnapshot.docs
          .map((doc) => VideoUnit.fromFirestore(doc.data(), doc.id))
          .toList();

      if (units.isNotEmpty) {
        // تحديث الكاش مع الوحدات الجديدة
        final existingUnits = _cachedVideoUnits
            .where((unit) => unit.subjectId != subjectId)
            .toList();
        _cachedVideoUnits = [...existingUnits, ...units];
        _lastUnitsUpdate = DateTime.now();

        // حفظ جميع الوحدات في التخزين الدائم
        await _persistentStorage.saveVideoUnits(_cachedVideoUnits);

        debugPrint(
          '✅ تم تحديث ${units.length} وحدة فيديو من Firebase للمادة $subjectId',
        );
      }

      return units;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث وحدات الفيديوهات من Firebase: $e');
      rethrow;
    }
  }

  /// تحديث دروس الفيديوهات من Firebase (للاستخدام في الخلفية)
  Future<List<VideoLesson>> refreshVideoLessonsFromFirebase(
    String unitId,
  ) async {
    try {
      debugPrint('🔄 تحديث دروس الفيديوهات من Firebase للوحدة: $unitId');

      final querySnapshot = await _firestore
          .collection('video_lessons')
          .where('unitId', isEqualTo: unitId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      final lessons = querySnapshot.docs
          .map((doc) => VideoLesson.fromFirestore(doc.data(), doc.id))
          .toList();

      if (lessons.isNotEmpty) {
        // تحديث الكاش مع الدروس الجديدة
        final existingLessons = _cachedVideoLessons
            .where((lesson) => lesson.unitId != unitId)
            .toList();
        _cachedVideoLessons = [...existingLessons, ...lessons];
        _lastLessonsUpdate = DateTime.now();

        // حفظ جميع الدروس في التخزين الدائم
        await _persistentStorage.saveVideoLessons(_cachedVideoLessons);

        debugPrint(
          '✅ تم تحديث ${lessons.length} درس فيديو من Firebase للوحدة $unitId',
        );
      }

      return lessons;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث دروس الفيديوهات من Firebase: $e');
      rethrow;
    }
  }

  /// تحديث فيديوهات من Firebase (للاستخدام في الخلفية)
  Future<List<Video>> refreshVideosFromFirebase(String lessonId) async {
    try {
      debugPrint('🔄 تحديث الفيديوهات من Firebase للدرس: $lessonId');

      final querySnapshot = await _firestore
          .collection('videos')
          .where('lessonId', isEqualTo: lessonId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      final videos = querySnapshot.docs
          .map((doc) => Video.fromFirestore(doc.data(), doc.id))
          .toList();

      if (videos.isNotEmpty) {
        // تحديث الكاش مع الفيديوهات الجديدة
        final existingVideos = _cachedVideos
            .where((video) => video.lessonId != lessonId)
            .toList();
        _cachedVideos = [...existingVideos, ...videos];
        _lastVideosUpdate = DateTime.now();

        // حفظ جميع الفيديوهات في التخزين الدائم
        await _persistentStorage.saveVideos(_cachedVideos);

        debugPrint(
          '✅ تم تحديث ${videos.length} فيديو من Firebase للدرس $lessonId',
        );
      }

      return videos;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث الفيديوهات من Firebase: $e');
      rethrow;
    }
  }

  /// الحصول على الوحدات المحفوظة في الكاش (بدون await)
  List<VideoUnit> getCachedVideoUnits(String subjectId) {
    return _cachedVideoUnits
        .where((unit) => unit.subjectId == subjectId)
        .toList();
  }

  /// الحصول على الدروس المحفوظة في الكاش (بدون await)
  List<VideoLesson> getCachedVideoLessons(String unitId) {
    return _cachedVideoLessons
        .where((lesson) => lesson.unitId == unitId)
        .toList();
  }

  /// الحصول على الفيديوهات المحفوظة في الكاش (بدون await)
  List<Video> getCachedVideos(String lessonId) {
    return _cachedVideos.where((video) => video.lessonId == lessonId).toList();
  }

  /// الحصول على جميع مواد الفيديو المحفوظة في الكاش (بدون await)
  List<VideoSubject> get cachedVideoSubjects => _cachedVideoSubjects;

  /// الحصول على الأقسام المدفوعة فقط (بدون تحديث تلقائي)
  Future<List<VideoSection>> getPaidVideoSections() async {
    try {
      // إذا كانت الأقسام محفوظة في الكاش، أرجعها فوراً (مثل ContentService)
      if (_cachedVideoSections.isNotEmpty) {
        final paidSections = _cachedVideoSections
            .where((s) => !s.isFree && s.isActive)
            .toList();
        paidSections.sort((a, b) => a.order.compareTo(b.order));
        debugPrint(
          '⚡ إرجاع ${paidSections.length} قسم فيديو مدفوع من الكاش فوراً',
        );
        return paidSections;
      }

      // محاولة تحميل الأقسام من التخزين الدائم أولاً
      final persistentSections = await _persistentStorage.loadVideoSections();
      if (persistentSections.isNotEmpty) {
        _cachedVideoSections = persistentSections;
        final paidSections = persistentSections
            .where((s) => !s.isFree && s.isActive)
            .toList();
        paidSections.sort((a, b) => a.order.compareTo(b.order));
        debugPrint(
          '⚡ تم تحميل ${paidSections.length} قسم فيديو مدفوع من التخزين الدائم',
        );
        return paidSections;
      }

      // إذا لم توجد في التخزين الدائم، حمل من التخزين المحلي القديم
      final offlineSections = await OfflineStorageService.instance
          .getOfflineVideoSections();

      if (offlineSections.isNotEmpty) {
        // حفظ في الكاش للمرات القادمة
        _cachedVideoSections = offlineSections;
        _lastSectionsUpdate = DateTime.now();

        final paidSections = offlineSections
            .where((s) => !s.isFree && s.isActive)
            .toList();
        // ترتيب الأقسام حسب الترتيب
        paidSections.sort((a, b) => a.order.compareTo(b.order));
        debugPrint(
          '⚡ تم تحميل ${paidSections.length} قسم فيديو مدفوع من التخزين المحلي الدائم',
        );
        return paidSections;
      }

      // تحميل أقسام الفيديو من الخدمة البسيطة فوراً
      final allSections = SimpleDataService.instance.getVideoSections();
      _cachedVideoSections = allSections;

      final paidSections = allSections
          .where((s) => !s.isFree && s.isActive)
          .toList();
      paidSections.sort((a, b) => a.order.compareTo(b.order));

      debugPrint(
        '⚡ تم تحميل ${paidSections.length} قسم فيديو فوراً من البيانات المحلية',
      );
      return paidSections;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الأقسام المدفوعة: $e');
      // محاولة أخيرة من التخزين المحلي
      final offlineSections = await OfflineStorageService.instance
          .getOfflineVideoSections();
      if (offlineSections.isNotEmpty) {
        final paidSections = offlineSections
            .where((s) => !s.isFree && s.isActive)
            .toList();
        paidSections.sort((a, b) => a.order.compareTo(b.order));
        debugPrint('📚 إرجاع البيانات المحلية بسبب فشل التحميل');
        return paidSections;
      }
      return [];
    }
  }

  /// إجبار إعادة تحميل أقسام الفيديوهات من Firebase (للتحديث اليدوي)
  Future<List<VideoSection>> forceReloadPaidVideoSections() async {
    try {
      debugPrint('🔄 إجبار إعادة تحميل أقسام الفيديوهات من Firebase...');
      final snapshot = await _firestore
          .collection('video_sections')
          .where('isActive', isEqualTo: true)
          .get();

      final allSections = snapshot.docs
          .map((doc) => VideoSection.fromFirestore(doc.data(), doc.id))
          .toList();

      // حفظ جميع الأقسام محلياً بشكل دائم
      await OfflineStorageService.instance.saveVideoSections(allSections);

      // تحديث الذاكرة المؤقتة أيضاً
      _cachedVideoSections = allSections;
      _lastSectionsUpdate = DateTime.now();

      final paidSections = allSections.where((s) => !s.isFree).toList();

      // ترتيب الأقسام حسب الترتيب
      paidSections.sort((a, b) => a.order.compareTo(b.order));

      debugPrint(
        '✅ تم إعادة تحميل وحفظ ${paidSections.length} قسم فيديو مدفوع من Firebase',
      );
      return paidSections;
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تحميل الأقسام المدفوعة: $e');
      rethrow;
    }
  }

  /// إضافة قسم فيديوهات جديد
  Future<String> addVideoSection(VideoSection section) async {
    try {
      final docRef = await _firestore
          .collection('video_sections')
          .add(section.toFirestore());

      notifyListeners();
      return docRef.id;
    } catch (e) {
      debugPrint('خطأ في إضافة قسم الفيديوهات: $e');
      rethrow;
    }
  }

  /// تحديث قسم فيديوهات
  Future<void> updateVideoSection(VideoSection section) async {
    try {
      await _firestore
          .collection('video_sections')
          .doc(section.id)
          .update(section.toFirestore());

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث قسم الفيديوهات: $e');
      rethrow;
    }
  }

  /// حذف قسم فيديوهات
  Future<void> deleteVideoSection(String sectionId) async {
    try {
      final batch = _firestore.batch();

      // حذف جميع المواد والوحدات والدروس والفيديوهات المرتبطة
      final subjectsSnapshot = await _firestore
          .collection('video_subjects')
          .where('sectionId', isEqualTo: sectionId)
          .get();

      for (final subjectDoc in subjectsSnapshot.docs) {
        await _deleteVideoSubjectData(subjectDoc.id, batch);
      }

      // حذف القسم نفسه
      batch.delete(_firestore.collection('video_sections').doc(sectionId));

      await batch.commit();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في حذف قسم الفيديوهات: $e');
      rethrow;
    }
  }

  // ===== إدارة مواد الفيديوهات =====

  /// الحصول على مواد الفيديوهات لقسم معين (بيانات محلية فورية!)
  Future<List<VideoSubject>> getVideoSubjects(String sectionId) async {
    try {
      // تحميل المواد من الخدمة البسيطة فوراً
      final allSubjects = SimpleDataService.instance.getVideoSubjects();
      final filteredSubjects = allSubjects
          .where((s) => s.sectionId == sectionId && s.isActive)
          .toList();
      filteredSubjects.sort((a, b) => a.order.compareTo(b.order));

      debugPrint(
        '⚡ إرجاع ${filteredSubjects.length} مادة فيديو فوراً من البيانات المحلية للقسم: $sectionId',
      );
      return filteredSubjects;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل مواد الفيديوهات: $e');
      return [];
    }
  }

  /// الحصول على جميع مواد الفيديوهات (بدون تحديث تلقائي)
  Future<List<VideoSubject>> getAllVideoSubjects() async {
    try {
      // إذا كانت المواد محفوظة في الكاش، أرجعها فوراً (مثل ContentService)
      if (_cachedVideoSubjects.isNotEmpty) {
        debugPrint(
          '⚡ إرجاع ${_cachedVideoSubjects.length} مادة فيديو من الكاش فوراً',
        );
        return _cachedVideoSubjects;
      }

      // تحميل المواد من الخدمة البسيطة فوراً
      _cachedVideoSubjects = SimpleDataService.instance.getVideoSubjects();
      debugPrint(
        '⚡ تم تحميل ${_cachedVideoSubjects.length} مادة فيديو فوراً من البيانات المحلية',
      );
      return _cachedVideoSubjects;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل جميع مواد الفيديوهات: $e');
      // محاولة أخيرة من التخزين المحلي
      final offlineSubjects = await OfflineStorageService.instance
          .getOfflineVideoSubjects();
      if (offlineSubjects.isNotEmpty) {
        debugPrint('📚 إرجاع البيانات المحلية بسبب فشل التحميل');
        return offlineSubjects;
      }
      return [];
    }
  }

  /// إجبار إعادة تحميل مواد الفيديوهات من Firebase (للتحديث اليدوي)
  Future<List<VideoSubject>> forceReloadAllVideoSubjects() async {
    try {
      debugPrint('🔄 إجبار إعادة تحميل مواد الفيديوهات من Firebase...');
      final snapshot = await _firestore
          .collection('video_subjects')
          .orderBy('name')
          .get();

      _cachedVideoSubjects = snapshot.docs
          .map((doc) => VideoSubject.fromFirestore(doc.data(), doc.id))
          .toList();
      _lastSubjectsUpdate = DateTime.now();

      debugPrint(
        '✅ تم إعادة تحميل ${_cachedVideoSubjects.length} مادة فيديو من Firebase',
      );
      return _cachedVideoSubjects;
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تحميل مواد الفيديوهات: $e');
      rethrow;
    }
  }

  /// إضافة مادة فيديوهات جديدة
  Future<String> addVideoSubject(VideoSubject subject) async {
    try {
      final docRef = await _firestore
          .collection('video_subjects')
          .add(subject.toFirestore());

      notifyListeners();
      return docRef.id;
    } catch (e) {
      debugPrint('خطأ في إضافة مادة الفيديوهات: $e');
      rethrow;
    }
  }

  /// تحديث مادة فيديوهات
  Future<void> updateVideoSubject(VideoSubject subject) async {
    try {
      await _firestore
          .collection('video_subjects')
          .doc(subject.id)
          .update(subject.toFirestore());

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث مادة الفيديوهات: $e');
      rethrow;
    }
  }

  /// حذف مادة فيديوهات
  Future<void> deleteVideoSubject(String subjectId) async {
    try {
      final batch = _firestore.batch();
      await _deleteVideoSubjectData(subjectId, batch);
      await batch.commit();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في حذف مادة الفيديوهات: $e');
      rethrow;
    }
  }

  /// حذف بيانات مادة الفيديوهات (مساعدة)
  Future<void> _deleteVideoSubjectData(
    String subjectId,
    WriteBatch batch,
  ) async {
    // حذف الوحدات والدروس والفيديوهات
    final unitsSnapshot = await _firestore
        .collection('video_units')
        .where('subjectId', isEqualTo: subjectId)
        .get();

    for (final unitDoc in unitsSnapshot.docs) {
      await _deleteVideoUnitData(unitDoc.id, batch);
    }

    // حذف المادة نفسها
    batch.delete(_firestore.collection('video_subjects').doc(subjectId));
  }

  // ===== إدارة وحدات الفيديوهات =====

  /// الحصول على وحدات الفيديوهات لمادة معينة (من التخزين المحلي أولاً)
  Future<List<VideoUnit>> getVideoUnits(String subjectId) async {
    try {
      // إذا كانت الوحدات محفوظة في الكاش، أرجع وحدات المادة فوراً
      if (_cachedVideoUnits.isNotEmpty) {
        final subjectUnits = _cachedVideoUnits
            .where((unit) => unit.subjectId == subjectId && unit.isActive)
            .toList();
        subjectUnits.sort((a, b) => a.order.compareTo(b.order));
        debugPrint(
          '⚡ إرجاع ${subjectUnits.length} وحدة فيديو من الكاش للمادة $subjectId',
        );
        return subjectUnits;
      }

      // تحميل الوحدات من الخدمة البسيطة فوراً
      _cachedVideoUnits = SimpleDataService.instance.getVideoUnits();

      final subjectUnits = _cachedVideoUnits
          .where((unit) => unit.subjectId == subjectId && unit.isActive)
          .toList();

      // ترتيب الوحدات حسب الترتيب
      subjectUnits.sort((a, b) => a.order.compareTo(b.order));
      debugPrint(
        '⚡ إرجاع ${subjectUnits.length} وحدة فيديو فوراً من البيانات المحلية للمادة $subjectId',
      );
      return subjectUnits;
    } catch (e) {
      debugPrint('خطأ في تحميل وحدات الفيديوهات: $e');
      return [];
    }
  }

  /// إضافة وحدة فيديوهات جديدة
  Future<String> addVideoUnit(VideoUnit unit) async {
    try {
      final docRef = await _firestore
          .collection('video_units')
          .add(unit.toFirestore());

      notifyListeners();
      return docRef.id;
    } catch (e) {
      debugPrint('خطأ في إضافة وحدة الفيديوهات: $e');
      rethrow;
    }
  }

  /// تحديث وحدة فيديوهات
  Future<void> updateVideoUnit(VideoUnit unit) async {
    try {
      await _firestore
          .collection('video_units')
          .doc(unit.id)
          .update(unit.toFirestore());

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث وحدة الفيديوهات: $e');
      rethrow;
    }
  }

  /// حذف وحدة فيديوهات
  Future<void> deleteVideoUnit(String unitId) async {
    try {
      final batch = _firestore.batch();
      await _deleteVideoUnitData(unitId, batch);
      await batch.commit();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في حذف وحدة الفيديوهات: $e');
      rethrow;
    }
  }

  /// حذف بيانات وحدة الفيديوهات (مساعدة)
  Future<void> _deleteVideoUnitData(String unitId, WriteBatch batch) async {
    // حذف الدروس والفيديوهات
    final lessonsSnapshot = await _firestore
        .collection('video_lessons')
        .where('unitId', isEqualTo: unitId)
        .get();

    for (final lessonDoc in lessonsSnapshot.docs) {
      await _deleteVideoLessonData(lessonDoc.id, batch);
    }

    // حذف الوحدة نفسها
    batch.delete(_firestore.collection('video_units').doc(unitId));
  }

  // ===== إدارة دروس الفيديوهات =====

  /// الحصول على دروس الفيديوهات لوحدة معينة (من التخزين المحلي أولاً)
  Future<List<VideoLesson>> getVideoLessons(String unitId) async {
    try {
      // إذا كانت الدروس محفوظة في الكاش، أرجع دروس الوحدة فوراً
      if (_cachedVideoLessons.isNotEmpty) {
        final unitLessons = _cachedVideoLessons
            .where((lesson) => lesson.unitId == unitId && lesson.isActive)
            .toList();
        unitLessons.sort((a, b) => a.order.compareTo(b.order));
        debugPrint(
          '⚡ إرجاع ${unitLessons.length} درس فيديو من الكاش للوحدة $unitId',
        );
        return unitLessons;
      }

      // تحميل الدروس من الخدمة البسيطة فوراً
      _cachedVideoLessons = SimpleDataService.instance.getVideoLessons();

      final unitLessons = _cachedVideoLessons
          .where((lesson) => lesson.unitId == unitId && lesson.isActive)
          .toList();

      // ترتيب الدروس حسب الترتيب
      unitLessons.sort((a, b) => a.order.compareTo(b.order));
      debugPrint(
        '⚡ إرجاع ${unitLessons.length} درس فيديو فوراً من البيانات المحلية للوحدة $unitId',
      );
      return unitLessons;
    } catch (e) {
      debugPrint('خطأ في تحميل دروس الفيديوهات: $e');
      return [];
    }
  }

  /// إضافة درس فيديوهات جديد
  Future<String> addVideoLesson(VideoLesson lesson) async {
    try {
      final docRef = await _firestore
          .collection('video_lessons')
          .add(lesson.toFirestore());

      notifyListeners();
      return docRef.id;
    } catch (e) {
      debugPrint('خطأ في إضافة درس الفيديوهات: $e');
      rethrow;
    }
  }

  /// تحديث درس فيديوهات
  Future<void> updateVideoLesson(VideoLesson lesson) async {
    try {
      await _firestore
          .collection('video_lessons')
          .doc(lesson.id)
          .update(lesson.toFirestore());

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث درس الفيديوهات: $e');
      rethrow;
    }
  }

  /// حذف درس فيديوهات
  Future<void> deleteVideoLesson(String lessonId) async {
    try {
      final batch = _firestore.batch();
      await _deleteVideoLessonData(lessonId, batch);
      await batch.commit();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في حذف درس الفيديوهات: $e');
      rethrow;
    }
  }

  /// حذف بيانات درس الفيديوهات (مساعدة)
  Future<void> _deleteVideoLessonData(String lessonId, WriteBatch batch) async {
    // حذف الفيديوهات
    final videosSnapshot = await _firestore
        .collection('videos')
        .where('lessonId', isEqualTo: lessonId)
        .get();

    for (final videoDoc in videosSnapshot.docs) {
      batch.delete(videoDoc.reference);
      // حذف الملف المحلي إذا كان موجوداً
      await _deleteLocalVideoFile(videoDoc.id);
    }

    // حذف الدرس نفسه
    batch.delete(_firestore.collection('video_lessons').doc(lessonId));
  }

  // ===== إدارة الفيديوهات =====

  /// الحصول على فيديوهات درس معين (من التخزين المحلي أولاً)
  Future<List<Video>> getVideos(String lessonId) async {
    try {
      // إذا كانت الفيديوهات محفوظة في الكاش، أرجع فيديوهات الدرس فوراً
      if (_cachedVideos.isNotEmpty) {
        final lessonVideos = _cachedVideos
            .where((video) => video.lessonId == lessonId && video.isActive)
            .toList();
        lessonVideos.sort((a, b) => a.order.compareTo(b.order));
        debugPrint(
          '⚡ إرجاع ${lessonVideos.length} فيديو من الكاش للدرس $lessonId',
        );
        return lessonVideos;
      }

      // تحميل الفيديوهات من الخدمة البسيطة فوراً
      _cachedVideos = SimpleDataService.instance.getVideos();

      final lessonVideos = _cachedVideos
          .where((video) => video.lessonId == lessonId && video.isActive)
          .toList();

      // ترتيب الفيديوهات حسب الترتيب
      lessonVideos.sort((a, b) => a.order.compareTo(b.order));
      debugPrint(
        '⚡ إرجاع ${lessonVideos.length} فيديو فوراً من البيانات المحلية للدرس $lessonId',
      );
      return lessonVideos;
    } catch (e) {
      debugPrint('خطأ في تحميل الفيديوهات: $e');
      return [];
    }
  }

  /// إضافة فيديو جديد (الروابط مشفرة مسبقاً)
  Future<String> addVideo(Video video) async {
    try {
      final docRef = await _firestore
          .collection('videos')
          .add(video.toFirestore());

      notifyListeners();
      return docRef.id;
    } catch (e) {
      debugPrint('خطأ في إضافة الفيديو: $e');
      rethrow;
    }
  }

  /// تحديث فيديو (الروابط مشفرة مسبقاً)
  Future<void> updateVideo(Video video) async {
    try {
      await _firestore
          .collection('videos')
          .doc(video.id)
          .update(video.toFirestore());

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث الفيديو: $e');
      rethrow;
    }
  }

  /// حذف فيديو
  Future<void> deleteVideo(String videoId) async {
    try {
      await _firestore.collection('videos').doc(videoId).delete();
      await _deleteLocalVideoFile(videoId);
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في حذف الفيديو: $e');
      rethrow;
    }
  }

  /// فك تشفير رابط الفيديو للمشاهدة
  Future<String?> decryptVideoUrl(String videoId, String encryptedUrl) async {
    try {
      final deviceId = await DeviceService.instance.getDeviceId();

      // محاولة فك التشفير
      final encryptedData = {
        'encryptedData': encryptedUrl,
        'hash': '', // سيتم تحديثه لاحقاً
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      };

      final decryptedData = _encryption.decryptVideoUrl(
        encryptedData,
        deviceId,
      );

      if (decryptedData != null && decryptedData['videoId'] == videoId) {
        return decryptedData['url'] as String;
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في فك تشفير رابط الفيديو: $e');
      return null;
    }
  }

  // ===== التحميل المحمي =====

  /// تحميل فيديو وحفظه مشفراً محلياً
  Future<bool> downloadVideo(
    Video video,
    VideoQuality quality, {
    Function(double, int, int)? onProgress,
  }) async {
    try {
      if (_videosDirectory == null) {
        await _initializeVideosDirectory();
      }

      // الحصول على الرابط المشفر للجودة المطلوبة
      final encryptedUrl = video.getEncryptedUrlForQuality(quality);
      if (encryptedUrl == null) {
        throw Exception('لا يوجد رابط متاح لهذه الجودة');
      }

      // فك تشفير الرابط باستخدام الطريقة المبسطة
      final originalUrl = _encryption.decryptText(encryptedUrl);
      if (originalUrl.isEmpty) {
        throw Exception('فشل في فك تشفير رابط الفيديو');
      }

      // إنشاء اسم ملف مشفر مع الجودة
      final deviceId = await DeviceService.instance.getDeviceId();
      final encryptedFileName = _encryption.generateEncryptedFileName(
        '${video.id}_${quality.name}',
        deviceId,
      );
      final filePath = '${_videosDirectory!.path}/$encryptedFileName';

      // إنشاء token للإلغاء
      final cancelToken = CancelToken();
      _downloadTokens[video.id] = cancelToken;

      // تحميل الفيديو
      await _dio.download(
        originalUrl,
        filePath,
        cancelToken: cancelToken,
        onReceiveProgress: (received, total) {
          if (total != -1 && onProgress != null) {
            onProgress(received / total, received, total);
          }
        },
      );

      // قراءة الملف وتشفيره
      final file = File(filePath);
      final videoData = await file.readAsBytes();
      final encryptedData = _encryption.encryptVideoData(videoData);

      // حفظ البيانات المشفرة
      await file.writeAsBytes(encryptedData);

      // إنشاء hash للتحقق من السلامة
      final hash = _encryption.generateHash(video.id + deviceId);

      // تحديث معلومات الفيديو في قاعدة البيانات المحلية
      await _updateLocalVideoInfo(
        '${video.id}_${quality.name}',
        filePath,
        hash,
      );

      return true;
    } catch (e) {
      debugPrint('خطأ في تحميل الفيديو: $e');
      return false;
    } finally {
      // إزالة token بعد انتهاء التحميل
      _downloadTokens.remove(video.id);
    }
  }

  /// إلغاء تحميل فيديو
  void cancelDownload(String videoId) {
    final cancelToken = _downloadTokens[videoId];
    if (cancelToken != null && !cancelToken.isCancelled) {
      cancelToken.cancel('تم إلغاء التحميل من قبل المستخدم');
      _downloadTokens.remove(videoId);
      debugPrint('🚫 تم إلغاء تحميل الفيديو: $videoId');
    }
  }

  /// قراءة فيديو محمل محلياً وفك تشفيره
  Future<Uint8List?> getLocalVideoData(String videoId) async {
    try {
      final localInfo = await _getLocalVideoInfo(videoId);
      if (localInfo == null) return null;

      final file = File(localInfo['path']!);
      if (!await file.exists()) return null;

      final encryptedData = await file.readAsBytes();
      return _encryption.decryptVideoData(encryptedData);
    } catch (e) {
      debugPrint('خطأ في قراءة الفيديو المحلي: $e');
      return null;
    }
  }

  /// قراءة فيديو محمل محلياً بجودة معينة وفك تشفيره
  Future<Uint8List?> getLocalVideoDataWithQuality(
    String videoId,
    VideoQuality quality,
  ) async {
    try {
      final localInfo = await _getLocalVideoInfo('${videoId}_${quality.name}');
      if (localInfo == null) return null;

      final file = File(localInfo['path']!);
      if (!await file.exists()) return null;

      // قراءة البيانات المشفرة
      final encryptedData = await file.readAsBytes();

      // فك التشفير
      final decryptedData = _encryption.decryptVideoData(encryptedData);

      return decryptedData;
    } catch (e) {
      debugPrint('خطأ في قراءة الفيديو المحلي بجودة ${quality.name}: $e');
      return null;
    }
  }

  /// حذف ملف فيديو محلي
  Future<void> _deleteLocalVideoFile(String videoId) async {
    try {
      final localInfo = await _getLocalVideoInfo(videoId);
      if (localInfo != null) {
        final file = File(localInfo['path']!);
        if (await file.exists()) {
          await file.delete();
        }
        await _removeLocalVideoInfo(videoId);
      }
    } catch (e) {
      debugPrint('خطأ في حذف الملف المحلي: $e');
    }
  }

  /// تحديث معلومات الفيديو المحلي
  Future<void> _updateLocalVideoInfo(
    String videoId,
    String path,
    String hash,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final videoInfo = {
        'path': path,
        'hash': hash,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      await prefs.setString('video_$videoId', jsonEncode(videoInfo));
      debugPrint('تم حفظ معلومات الفيديو المحلي: $videoId');
    } catch (e) {
      debugPrint('خطأ في حفظ معلومات الفيديو: $e');
    }
  }

  /// الحصول على معلومات الفيديو المحلي
  Future<Map<String, String>?> _getLocalVideoInfo(String videoId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final videoInfoString = prefs.getString('video_$videoId');
      if (videoInfoString == null) return null;

      final videoInfo = jsonDecode(videoInfoString) as Map<String, dynamic>;
      return {
        'path': videoInfo['path'] as String,
        'hash': videoInfo['hash'] as String,
        'timestamp': videoInfo['timestamp'].toString(),
      };
    } catch (e) {
      debugPrint('خطأ في قراءة معلومات الفيديو: $e');
      return null;
    }
  }

  /// إزالة معلومات الفيديو المحلي
  Future<void> _removeLocalVideoInfo(String videoId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('video_$videoId');
      debugPrint('تم حذف معلومات الفيديو المحلي: $videoId');
    } catch (e) {
      debugPrint('خطأ في حذف معلومات الفيديو: $e');
    }
  }

  /// التحقق من وجود فيديو محمل محلياً
  Future<bool> isVideoDownloaded(String videoId) async {
    final localInfo = await _getLocalVideoInfo(videoId);
    if (localInfo == null) return false;

    final file = File(localInfo['path']!);
    return await file.exists();
  }

  /// التحقق من وجود فيديو محمل محلياً بجودة معينة
  Future<bool> isVideoDownloadedWithQuality(
    String videoId,
    VideoQuality quality,
  ) async {
    final localInfo = await _getLocalVideoInfo('${videoId}_${quality.name}');
    if (localInfo == null) {
      debugPrint('لا توجد معلومات محلية للفيديو: ${videoId}_${quality.name}');
      return false;
    }

    final file = File(localInfo['path']!);
    final exists = await file.exists();
    debugPrint('فحص وجود الملف: ${localInfo['path']} - موجود: $exists');
    return exists;
  }

  /// الحصول على قائمة الجودات المحملة لفيديو معين
  Future<List<VideoQuality>> getDownloadedQualities(String videoId) async {
    final downloadedQualities = <VideoQuality>[];

    for (final quality in VideoQuality.values) {
      if (await isVideoDownloadedWithQuality(videoId, quality)) {
        downloadedQualities.add(quality);
        debugPrint('تم العثور على جودة محملة: $videoId - ${quality.name}');
      }
    }

    debugPrint(
      'إجمالي الجودات المحملة لـ $videoId: ${downloadedQualities.length}',
    );
    return downloadedQualities;
  }

  /// الحصول على أفضل جودة محملة لفيديو معين
  Future<VideoQuality?> getBestDownloadedQuality(String videoId) async {
    final downloadedQualities = await getDownloadedQualities(videoId);
    if (downloadedQualities.isEmpty) return null;

    // ترتيب الجودات من الأعلى إلى الأقل
    final sortedQualities = [...downloadedQualities];
    sortedQualities.sort((a, b) => b.index.compareTo(a.index));

    return sortedQualities.first;
  }

  /// الحصول على مسار الملف المحلي لفيديو وجودة معينة
  Future<String?> getLocalVideoPath(
    String videoId,
    VideoQuality quality,
  ) async {
    try {
      final localInfo = await _getLocalVideoInfo('${videoId}_${quality.name}');
      if (localInfo != null) {
        final path = localInfo['path'];
        if (path != null && await File(path).exists()) {
          return path;
        }
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على مسار الملف المحلي: $e');
      return null;
    }
  }

  /// فك تشفير فيديو محلي للتشغيل - نظام محسن
  Future<String?> getDecryptedVideoForPlayback(
    String videoId,
    VideoQuality quality,
  ) async {
    try {
      debugPrint('🎬 بدء فك تشفير فيديو للتشغيل: $videoId - ${quality.name}');

      // الحصول على مسار الملف المشفر
      final encryptedPath = await getLocalVideoPath(videoId, quality);
      if (encryptedPath == null) {
        debugPrint('❌ لم يتم العثور على الملف المشفر');
        return null;
      }

      // إنشاء مسار للملف المفكوك التشفير المؤقت
      final directory = await getApplicationDocumentsDirectory();
      final tempDir = Directory('${directory.path}/temp_videos');
      if (!await tempDir.exists()) {
        await tempDir.create(recursive: true);
      }

      final tempVideoPath =
          '${tempDir.path}/${videoId}_${quality.name}_temp.mp4';

      // التحقق من وجود الملف المؤقت المفكوك التشفير مسبقاً
      final tempFile = File(tempVideoPath);
      if (await tempFile.exists()) {
        debugPrint('✅ تم العثور على ملف مؤقت مفكوك التشفير: $tempVideoPath');
        return tempVideoPath;
      }

      // قراءة الملف المشفر
      final encryptedFile = File(encryptedPath);
      if (!await encryptedFile.exists()) {
        debugPrint('❌ الملف المشفر غير موجود: $encryptedPath');
        return null;
      }

      debugPrint('📁 قراءة الملف المشفر: $encryptedPath');
      final encryptedBytes = await encryptedFile.readAsBytes();
      debugPrint('📊 حجم الملف المشفر: ${encryptedBytes.length} bytes');

      // فك التشفير باستخدام النظام المحسن
      final decryptedBytes = _encryption.decryptVideoData(encryptedBytes);
      debugPrint('📊 حجم الملف بعد فك التشفير: ${decryptedBytes.length} bytes');

      // كتابة الملف المفكوك التشفير
      await tempFile.writeAsBytes(decryptedBytes);

      debugPrint('✅ تم فك تشفير الفيديو بنجاح: $tempVideoPath');
      return tempVideoPath;
    } catch (e) {
      debugPrint('❌ خطأ في فك تشفير الفيديو للتشغيل: $e');
      return null;
    }
  }

  /// تنظيف معلومات الفيديوهات المحذوفة من SharedPreferences
  Future<void> _cleanupOrphanedVideoInfo() async {
    try {
      debugPrint('🧹 بدء تنظيف معلومات الفيديوهات المحذوفة...');

      final prefs = await SharedPreferences.getInstance();
      final keys = prefs
          .getKeys()
          .where((key) => key.startsWith('video_'))
          .toList();

      for (final key in keys) {
        final videoInfoString = prefs.getString(key);
        if (videoInfoString != null) {
          try {
            final videoInfo =
                jsonDecode(videoInfoString) as Map<String, dynamic>;
            final filePath = videoInfo['path'] as String?;

            if (filePath != null) {
              final file = File(filePath);
              if (!await file.exists()) {
                await prefs.remove(key);
                debugPrint('🗑️ تم حذف معلومات فيديو محذوف: $key');
              }
            }
          } catch (e) {
            // حذف المعلومات التالفة
            await prefs.remove(key);
            debugPrint('🗑️ تم حذف معلومات فيديو تالفة: $key');
          }
        }
      }

      debugPrint('✅ تم تنظيف معلومات الفيديوهات المحذوفة');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف معلومات الفيديوهات: $e');
    }
  }

  /// حذف جميع الملفات المشفرة القديمة وإعادة تعيين حالة التحميل
  Future<void> clearAllDownloadedVideos() async {
    try {
      debugPrint('🧹 بدء حذف جميع الملفات المحملة...');

      // حذف مجلد الفيديوهات المحملة
      final directory = await getApplicationDocumentsDirectory();
      final videosDir = Directory('${directory.path}/encrypted_videos');
      if (await videosDir.exists()) {
        await videosDir.delete(recursive: true);
        debugPrint('✅ تم حذف مجلد الفيديوهات المشفرة');
      }

      // حذف مجلد الفيديوهات المؤقتة
      final tempDir = Directory('${directory.path}/temp_videos');
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
        debugPrint('✅ تم حذف مجلد الفيديوهات المؤقتة');
      }

      // إعادة تعيين حالة التحميل في SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs
          .getKeys()
          .where((key) => key.startsWith('video_'))
          .toList();
      for (final key in keys) {
        await prefs.remove(key);
      }

      debugPrint('✅ تم حذف جميع الملفات وإعادة تعيين حالة التحميل');
    } catch (e) {
      debugPrint('❌ خطأ في حذف الملفات: $e');
    }
  }

  /// الحصول على حجم جميع الفيديوهات المحملة
  Future<int> getTotalDownloadedSize() async {
    try {
      if (_videosDirectory == null || !await _videosDirectory!.exists()) {
        return 0;
      }

      int totalSize = 0;
      final files = await _videosDirectory!.list().toList();

      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          totalSize += stat.size;
        }
      }

      return totalSize;
    } catch (e) {
      debugPrint('خطأ في حساب حجم الفيديوهات: $e');
      return 0;
    }
  }

  /// حذف فيديو محمل بجودة معينة
  Future<bool> deleteDownloadedVideo(
    String videoId,
    VideoQuality quality,
  ) async {
    try {
      debugPrint('🗑️ بدء حذف الفيديو المحمل: $videoId - ${quality.name}');

      // حذف الملف المشفر
      final localInfo = await _getLocalVideoInfo('${videoId}_${quality.name}');
      if (localInfo != null) {
        final file = File(localInfo['path']!);
        if (await file.exists()) {
          await file.delete();
          debugPrint('✅ تم حذف الملف المشفر: ${localInfo['path']}');
        }

        // حذف معلومات الفيديو من SharedPreferences
        await _removeLocalVideoInfo('${videoId}_${quality.name}');
      }

      // حذف الملف المؤقت المفكوك التشفير إذا كان موجوداً
      final directory = await getApplicationDocumentsDirectory();
      final tempDir = Directory('${directory.path}/temp_videos');
      if (await tempDir.exists()) {
        final tempVideoPath =
            '${tempDir.path}/${videoId}_${quality.name}_temp.mp4';
        final tempFile = File(tempVideoPath);
        if (await tempFile.exists()) {
          await tempFile.delete();
          debugPrint('✅ تم حذف الملف المؤقت: $tempVideoPath');
        }
      }

      debugPrint('✅ تم حذف الفيديو المحمل بنجاح: $videoId - ${quality.name}');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في حذف الفيديو المحمل: $e');
      return false;
    }
  }

  /// حذف جميع الجودات المحملة لفيديو معين
  Future<bool> deleteAllDownloadedQualities(String videoId) async {
    try {
      debugPrint('🗑️ بدء حذف جميع الجودات المحملة للفيديو: $videoId');

      bool allDeleted = true;
      final downloadedQualities = await getDownloadedQualities(videoId);

      for (final quality in downloadedQualities) {
        final deleted = await deleteDownloadedVideo(videoId, quality);
        if (!deleted) {
          allDeleted = false;
        }
      }

      if (allDeleted) {
        debugPrint('✅ تم حذف جميع الجودات المحملة للفيديو: $videoId');
      } else {
        debugPrint('⚠️ تم حذف بعض الجودات فقط للفيديو: $videoId');
      }

      return allDeleted;
    } catch (e) {
      debugPrint('❌ خطأ في حذف جميع الجودات المحملة: $e');
      return false;
    }
  }

  /// الحصول على حجم فيديو محمل بجودة معينة
  Future<int> getDownloadedVideoSize(
    String videoId,
    VideoQuality quality,
  ) async {
    try {
      final localInfo = await _getLocalVideoInfo('${videoId}_${quality.name}');
      if (localInfo != null) {
        final file = File(localInfo['path']!);
        if (await file.exists()) {
          final stat = await file.stat();
          return stat.size;
        }
      }
      return 0;
    } catch (e) {
      debugPrint('خطأ في حساب حجم الفيديو المحمل: $e');
      return 0;
    }
  }

  /// الحصول على إجمالي حجم جميع الجودات المحملة لفيديو معين
  Future<int> getTotalDownloadedSizeForVideo(String videoId) async {
    try {
      int totalSize = 0;
      final downloadedQualities = await getDownloadedQualities(videoId);

      for (final quality in downloadedQualities) {
        final size = await getDownloadedVideoSize(videoId, quality);
        totalSize += size;
      }

      return totalSize;
    } catch (e) {
      debugPrint('خطأ في حساب إجمالي حجم الفيديو المحمل: $e');
      return 0;
    }
  }

  /// تنظيف الملفات المؤقتة القديمة
  Future<void> cleanupTempFiles() async {
    try {
      debugPrint('🧹 بدء تنظيف الملفات المؤقتة...');

      final directory = await getApplicationDocumentsDirectory();
      final tempDir = Directory('${directory.path}/temp_videos');

      if (await tempDir.exists()) {
        final files = await tempDir.list().toList();
        final now = DateTime.now();

        for (final file in files) {
          if (file is File) {
            final stat = await file.stat();
            final age = now.difference(stat.modified);

            // حذف الملفات الأقدم من ساعة واحدة
            if (age.inHours > 1) {
              await file.delete();
              debugPrint('🗑️ تم حذف ملف مؤقت قديم: ${file.path}');
            }
          }
        }
      }

      debugPrint('✅ تم تنظيف الملفات المؤقتة');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف الملفات المؤقتة: $e');
    }
  }
}
