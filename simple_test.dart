import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

void main() {
  runApp(SimpleTestApp());
}

class SimpleTestApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          title: 'اختبار بسيط',
          theme: ThemeData(
            primarySwatch: Colors.blue,
          ),
          home: SimpleTestScreen(),
          debugShowCheckedModeBanner: false,
        );
      },
    );
  }
}

class SimpleTestScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    bool isDesktop = MediaQuery.of(context).size.width > 800;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'اختبار النظام التكيفي',
          style: TextStyle(
            fontSize: isDesktop ? 24.sp : 20.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(isDesktop ? 24.w : 16.w),
        child: Column(
          children: [
            // معلومات الجهاز
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'معلومات الجهاز',
                    style: TextStyle(
                      fontSize: isDesktop ? 20.sp : 18.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                  SizedBox(height: 12.h),
                  Text(
                    'نوع الجهاز: ${isDesktop ? "كمبيوتر" : "هاتف"}',
                    style: TextStyle(fontSize: isDesktop ? 16.sp : 14.sp),
                  ),
                  Text(
                    'عرض الشاشة: ${MediaQuery.of(context).size.width.toInt()}px',
                    style: TextStyle(fontSize: isDesktop ? 16.sp : 14.sp),
                  ),
                  Text(
                    'ارتفاع الشاشة: ${MediaQuery.of(context).size.height.toInt()}px',
                    style: TextStyle(fontSize: isDesktop ? 16.sp : 14.sp),
                  ),
                ],
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // اختبار الأزرار
            Text(
              'اختبار الأزرار',
              style: TextStyle(
                fontSize: isDesktop ? 20.sp : 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            SizedBox(height: 16.h),
            
            // أزرار الاختبار
            Expanded(
              child: GridView.count(
                crossAxisCount: isDesktop ? 4 : 2,
                crossAxisSpacing: isDesktop ? 20.w : 12.w,
                mainAxisSpacing: isDesktop ? 20.h : 12.h,
                children: List.generate(6, (index) {
                  return ElevatedButton(
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('تم الضغط على الزر ${index + 1}'),
                          duration: Duration(seconds: 1),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[600],
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      padding: EdgeInsets.all(isDesktop ? 16.w : 12.w),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.star,
                          size: isDesktop ? 32.sp : 24.sp,
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          'زر ${index + 1}',
                          style: TextStyle(
                            fontSize: isDesktop ? 16.sp : 14.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
