import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

void main() {
  runApp(TestAdaptiveApp());
}

class TestAdaptiveApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      // أبعاد الهاتف: 375x812
      // أبعاد الكمبيوتر: 1200x800
      designSize: Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          title: 'اختبار النظام التكيفي',
          theme: ThemeData(
            primarySwatch: Colors.blue,
            fontFamily: 'Cairo',
          ),
          home: TestAdaptiveScreen(),
          debugShowCheckedModeBanner: false,
        );
      },
    );
  }
}

class TestAdaptiveScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // تحديد نوع الجهاز
    bool isDesktop = MediaQuery.of(context).size.width > 800;
    
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: Text(
          'اختبار النظام التكيفي',
          style: TextStyle(
            fontSize: isDesktop ? 24.sp : 20.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue[600],
        elevation: 0,
        centerTitle: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(isDesktop ? 24.w : 16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات الجهاز
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(isDesktop ? 20.w : 16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'معلومات الجهاز',
                    style: TextStyle(
                      fontSize: isDesktop ? 20.sp : 18.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                  SizedBox(height: 12.h),
                  _buildInfoRow('نوع الجهاز:', isDesktop ? 'كمبيوتر' : 'هاتف', isDesktop),
                  _buildInfoRow('عرض الشاشة:', '${MediaQuery.of(context).size.width.toInt()}px', isDesktop),
                  _buildInfoRow('ارتفاع الشاشة:', '${MediaQuery.of(context).size.height.toInt()}px', isDesktop),
                  _buildInfoRow('نسبة الكثافة:', '${MediaQuery.of(context).devicePixelRatio.toStringAsFixed(1)}', isDesktop),
                ],
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // اختبار الأزرار
            Text(
              'اختبار الأزرار التكيفية',
              style: TextStyle(
                fontSize: isDesktop ? 20.sp : 18.sp,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            
            SizedBox(height: 16.h),
            
            // شبكة الأزرار
            Expanded(
              child: GridView.builder(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: isDesktop ? 4 : 2,
                  crossAxisSpacing: isDesktop ? 20.w : 12.w,
                  mainAxisSpacing: isDesktop ? 20.h : 12.h,
                  childAspectRatio: isDesktop ? 1.2 : 1.0,
                ),
                itemCount: 8,
                itemBuilder: (context, index) {
                  return _buildTestButton(
                    'زر ${index + 1}',
                    Icons.star,
                    Colors.primaries[index % Colors.primaries.length],
                    isDesktop,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildInfoRow(String label, String value, bool isDesktop) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isDesktop ? 16.sp : 14.sp,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            value,
            style: TextStyle(
              fontSize: isDesktop ? 16.sp : 14.sp,
              color: Colors.blue[600],
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildTestButton(String title, IconData icon, Color color, bool isDesktop) {
    return Builder(
      builder: (context) => Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withOpacity(0.8),
              color,
            ],
          ),
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.3),
              spreadRadius: 1,
              blurRadius: 8,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(12.r),
            onTap: () {
              // عرض رسالة
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم الضغط على $title'),
                  backgroundColor: color,
                  duration: Duration(seconds: 1),
                ),
              );
            },
          child: Padding(
            padding: EdgeInsets.all(isDesktop ? 16.w : 12.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: isDesktop ? 32.sp : 24.sp,
                  color: Colors.white,
                ),
                SizedBox(height: 8.h),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: isDesktop ? 16.sp : 14.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
